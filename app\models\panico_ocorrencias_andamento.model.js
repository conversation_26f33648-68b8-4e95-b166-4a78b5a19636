const sql = require("./db.js");

// constructor
const Panico_ocorrencias_andamento = function(panico_ocorrencias_andamento) {
  this.id = panico_ocorrencias_andamento.id;
};

Panico_ocorrencias_andamento.getAll = result => {
  sql.query("SELECT * FROM `tbl_panico` INNER JOIN `api_user` ON (`tbl_panico`.`USER_ID` = `api_user`.`ID_USUARIO`)where `tbl_panico`.`STATUS` = '1';", (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(null, err);
      return;
    }
    console.log("customers: ", res);
    result(null, res);
  });
};


module.exports = Panico_ocorrencias_andamento;
