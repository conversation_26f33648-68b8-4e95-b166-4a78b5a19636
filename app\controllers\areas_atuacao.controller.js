const area = require("../models/areas_atuacao.model.js");
const area_model = require("../models/areas_atuacao.model.js");



// Cria e salva uma nova area
exports.salvararea = (req, res) => {
  
 
  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"
     
    });

    return;
  }

  // Cria uma area

  const area = new area_model({
    nome_da_area: req.body.nome_da_area,
    ponto_de_referencia: req.body.ponto_de_referencia,
    raio: req.body.raio,
    latitude: req.body.latitude,
    longitude: req.body.longitude,
    ativa_inativa: req.body.ativa_inativa
  });

  // Salva area no banco de dados
  area_model.salvar_area(area, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Ocorreu algum erro ao criar area."
      });
    else res.send(data);
  });

};

// Busca uma unica area pelo seu id  
exports.buscararea = (req, res) => {
  // console.log(req.body)
  area.buscar_area(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrada area com id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar area com id " + req.body
        });
      }
    } else res.send(data);
  });
};

//mostra todas as areas cadastradas
exports.todasareas = (req, res) => {
  area.todas_areas((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Algum erro ocorreu ao tentar recuperar areas"
      });
    else res.send(data);
  });
};
// oi
//atualiza os dados de uma area identificada pelo id na chamada
exports.atualizararea = (req, res) => {
  //validar solicitação
  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"
    });
  }

  console.log(req.body);
//verificando se há erros na busca ou na atualização para se não, retornar os dados atualizados
  area.atualizar_area(req.body, (err, data) => {
      if (err) {
        if (err.kind === "not_found") {
          res.status(404).send({
            message: `Não encontrada area com id ${req.params.atualizararea}.`
          });
        } else {
          res.status(500).send({
            message: "erro ao atualizar area com id " + req.params.atualizararea
          });
        }
      } else res.send(data);
    }
  );
};
