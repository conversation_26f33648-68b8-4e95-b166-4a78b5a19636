//import supertest from 'supertest'
//import server from './server.js'

//const app = module.app;
//require('supertest');
const { app } = require("express");
var { supertest } = require('supertest');
//import supertest from 'supertest';
//const { request } = require("supertest");
//cons request from 'supertest';
//const app = express();

describe("POST /enviar_viatura", () => {

    describe ("dado o id da ocorrência", () => {
        test("id da ocorrencia", () => {
            expect(true).toBe(true);
        });
    })

    describe ("dado o id da vitaura", () => {
        test("id da viatura", () => {
            expect(true).toBe(true);
        });
    })

    test("passou tudo", () => {
        expect(true).toBe(true);
    });

})

/*
describe('GET /ocorrencias', function () {
    it('respond with json containing a list of all users', function (done) {
        request(app)
            .get('/ocorrencias')
            .set('Accept', 'application/json')
            .expect(200, done);
            //.expect('Content-Type', /json/)
            
    });
});

/*
describe("GET /ocorrencias", () => {
    test("should respond with a 200 status code", async () => {
        const request = await request(server).get("/ocorrencias").
            expect(outro.statusCode).toBe(200)
      })
})
*/