module.exports = app => {
  
    const qrcodes = require("../controllers/qrcode_gen.controller.js");
  
  
    //rota para salvar uma nova qrcode 
    app.post("/salvar_qrcode", qrcodes.salvarqrcode)

    //rota para buscar uma qrcode pelo id
    app.post("/buscar_qrcode", qrcodes.buscarqrcode)

    //rota para retornar todas as qrcodes
    app.get("/todas_qrcodes", qrcodes.todasqrcodes)

    //atualizar os dados de uma qrcode a partir do seu id
    app.put("/atualizar_qrcode", qrcodes.atualizarqrcode)  
    
    // // Delete a Customer with customerId
    // app.delete("/customers/:customerId", customers.delete);

    
  };
  