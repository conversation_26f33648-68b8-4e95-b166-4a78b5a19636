const qrcode = require("../models/qrcode_gen.model.js");
const qrcode_model = require("../models/qrcode_gen.model.js");



// Cria e salva uma nova qrcode
exports.salvarqrcode = (req, res) => {
  
 
  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"
     
    });

    return;
  }

  // Cria uma qrcode

  const qrcode = new qrcode_model({
    
    id_fzd: req.body.id_fzd,
    nome_da_fazenda: req.body.nome_da_fazenda,
    data_da_criacao: req.body.data_da_criacao,
    hora_da_criacao: req.body.hora_da_criacao
    
  });

  // Salva qrcode no banco de dados
  qrcode_model.salvar_qrcode(qrcode, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Ocorreu algum erro ao criar qrcode."
      });
    else res.send(data);
  });

};

// Busca uma unica qrcode pelo seu id
exports.buscarqrcode = (req, res) => {
  // console.log(req.body)
  qrcode.buscar_qrcode(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrada qrcode com id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar qrcode com id " + req.body
        });
      }
    } else res.send(data);
  });
};

//mostra todas as qrcodes cadastradas
exports.todasqrcodes = (req, res) => {
  qrcode.todas_qrcodes((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Algum erro ocorreu ao tentar recuperar qrcodes"
      });
    else res.send(data);
  });
};

//atualiza os dados de uma qrcode identificada pelo id na chamada
exports.atualizarqrcode = (req, res) => {
  //validar solicitação
  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"
    });
  }

  console.log(req.body);
//verificando se há erros na busca ou na atualização para se não, retornar os dados atualizados
  qrcode.atualizar_qrcode(req.body, (err, data) => {
      if (err) {
        if (err.kind === "not_found") {
          res.status(404).send({
            message: `Não encontrada qrcode com id ${req.params.atualizarqrcode}.`
          });
        } else {
          res.status(500).send({
            message: "erro ao atualizar qrcode com id " + req.params.atualizarqrcode
          });
        }
      } else res.send(data);
    }
  );
};

// //delete uma qrcode com o id especificado na chamada
// exports.delete = (req, res) => {
//   Customer.remove(req.params.customerId, (err, data) => {
//     if (err) {
//       if (err.kind === "not_found") {
//         res.status(404).send({
//           message: `Not found Customer with id ${req.params.customerId}.`
//         });
//       } else {
//         res.status(500).send({
//           message: "Could not delete Customer with id " + req.params.customerId
//         });
//       }
//     } else res.send({ message: `Customer was deleted successfully!` });
//   });
// };


// //delete todas as qrcodes do banco de dados.
// exports.deleteAll = (req, res) => {
//   Customer.removeAll((err, data) => {
//     if (err)
//       res.status(500).send({
//         message:
//           err.message || "Some error occurred while removing all customers."
//       });
//     else res.send({ message: `All Customers were deleted successfully!` });
//   });
// };