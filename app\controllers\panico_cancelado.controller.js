const Panico_cancelado = require("../models/panico_cancelado.model.js");
var requestIp = require('request-ip');
const axios = require('axios')

exports.disparo_panico_cancelado = (req, res) => {
  console.log(req.body)
  let token;
  var clientIp = requestIp.getClientIp(req);
  if (!req.body) {
    res.status(400).send({
      message: "Content can not be empty!"
    });

    return;
  }

  if (!req.body.resumo) {
    res.status(400).send({
      message: "Resumo não pode estar vazio"
    })

    return;
  }

  const panico_finalizado = new Panico_cancelado({
    id: req.body.id,
    resumo: req.body.resumo
  });

  Panico_cancelado.disparo(panico_finalizado, async (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while creating the Customer."
      });
    else {
      await axios.post(`https://activecloud.cloud/api/v1/auth`, {
        email: "<EMAIL>",
        password: "makaki<PERSON><PERSON><PERSON>"
      })
        .then(res => {
          console.log(res.data)
          token = res.data
        })
        .catch(err => {
          console.log(err)
        })
      await axios.put(`https://activecloud.cloud/api/v1/devices/003173`, {
        cmd: "disarm"
      }, {
        headers: {
          "X-Access-Token": token.token
        }

      })
        .then(res => {
          response_arm = res.data
        })
        .catch(err => {
          console.log(err)
        })
      res.send({ "resposta": "OK" });
    }
    // socketio.emit('Atenção viatura a caminho', { "Aviso": "Teste", "id_alerta": data });

  });

};
