const log_visita = require("../models/log_visita_fazenda.model.js");
const log_visita_model = require("../models/log_visita_fazenda.model.js");


// Cria e salva uma nova log_visita
exports.salvarlogvisita = (req, res) => {


  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"

    });

    return;
  }

  // Cria uma log_visita
  const log_visita = new log_visita_model({
    dia: req.body.dia,
    hora: req.body.hora,
    policial: req.body.policial,
    qrcode: req.body.qrcode,
    geo_pos_leitor: req.body.geo_pos_leitor,
    id_fzd: req.body.id_fzd

  });

  // Salva log_visita no banco de dados
  log_visita_model.salvar_log_visita(log_visita, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Ocorreu algum erro ao criar log_visita."
      });
    else res.send(data);
  });

};

// Busca uma unica log_visita pelo seu id
exports.buscarlogvisita = (req, res) => {

  log_visita.buscar_log_visita(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrada log_visita com id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar log_visita com id " + req.body
        });
      }
    } else res.send(data);
  });
};

//mostra todas as log_visitas cadastradas
exports.todaslogvisitas = (req, res) => {
  log_visita.todas_log_visitas((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Algum erro ocorreu ao tentar recuperar log_visitas"
      });
    else res.send(data);
  });
};

//atualiza os dados de uma log_visita identificada pelo id na chamada
exports.atualizarlogvisita = (req, res) => {
  //validar solicitação
  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"
    });
  }

  console.log(req.body);
  //verificando se há erros na busca ou na atualização para se não, retornar os dados atualizados
  log_visita.atualizar_log_visita(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Não encontrada log_visita com id ${req.params.atualizarlog_visita}.`
        });
      } else {
        res.status(500).send({
          message: "erro ao atualizar log_visita com id " + req.params.atualizarlog_visita
        });
      }
    } else res.send(data);
  }
  );
};

//delete uma log_visita com o id especificado na chamada
exports.deletarlogvisita = (req, res) => {

  log_visita.deletar_log_visita(req.body.id, (err, data) => {

    if (err) {

      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found Customer with id ${req.params.customerId}.`
        });
      } else {
        res.status(500).send({
          message: "Could not delete Customer with id " + req.params.customerId
        });
      }
      
    } else res.send({ message: `Customer was deleted successfully!` });
    
  });

};





