const sql = require("./db.js");
const policial = function (policial) {
  this.matricula = policial.matricula;
  this.senha = policial.senha;
  this.posto_graduacao = policial.posto_graduacao;
  this.nome = policial.nome;
  this.foto = policial.foto;
  this.unidade = policial.unidade;
  this.email = policial.email;
  this.telefone = policial.telefone;
  this.coordenador = policial.coordenador;
  this.ativo_inativo = policial.ativo_inativo;

};

//salvar policial
policial.salvar_policial = (novo_policial, result) => {
  sql.query(`SELECT * FROM z_pmba_cad_pm WHERE ((email = '${novo_policial.email}') OR (matricula = '${novo_policial.matricula}'))`, novo_policial, (err, res) => {
    if (res.length){
      result(null, {
        errmessage: 'Policial já cadastrado anteriormente'
      });
      return;
    } else {


      sql.query("INSERT INTO z_pmba_cad_pm SET ?", novo_policial, (err2, res2) => {
        if (err2) {
          console.log("error: ", err2);
          result(err2, null);
          return;
        }

        console.log("policial criado: ", { id: res2.insertId, ...novo_policial });
        result(null, { id: res2.insertId, ...novo_policial });
      });


    }
  });

};

//buscar uma unica policial pelo id
policial.buscar_policial = (json, result) => {
  sql.query(`SELECT * FROM z_pmba_cad_pm WHERE id = ${json.id}`, (err, res) => {
    //se ocorrer um erro
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca do policial suceder
    if (res.length) {
      console.log("policial encontrado: ", res[0]);
      result(null, res[0]);
      return;
    }

    //quando não encontrado o policial pelo id
    result({ kind: "not_found" }, null);
  });
};

//buscar todos os policials cadastrados
policial.todos_policiais = result => {
  sql.query("SELECT * FROM z_pmba_cad_pm;", (err, res) => {
    if (err) {
      //se ocorrer um erro
      console.log("error: ", err);
      result(null, err);
      return;
    }
    //se a busca de todos os policiais suceder 
    console.log("policiais: ", res);
    result(null, res);
  });
};

//atualiza os dados de um policial pelo seu id
policial.atualizar_policial = (policial, result) => {
  console.log(policial)
  sql.query(
    "UPDATE z_pmba_cad_pm SET matricula = ?, senha = ?, posto_graduacao = ?, nome = ?, foto = ?, unidade = ?, email = ?, telefone = ?, coordenador = ?, ativo_inativo = ? WHERE id = ?",
    [policial.matricula, policial.senha, policial.posto_graduacao, policial.nome, policial.foto, policial.unidade, policial.email, policial.telefone, policial.coordenador, policial.ativo_inativo, policial.id],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }

      if (res.affectedRows == 0) {
        //não encontrado policial com o id
        result({ kind: "policial não encontrado" }, null);
        return;
      }

      result(null, "OK" );
    }
  );
};

// //delete uma policial com o id especificado na chamada
// Customer.remove = (id, result) => {
//   sql.query("DELETE FROM customers WHERE id = ?", id, (err, res) => {
//     if (err) {
//       console.log("error: ", err);
//       result(null, err);
//       return;
//     }

//     if (res.affectedRows == 0) {
//       // not found Customer with the id
//       result({ kind: "not_found" }, null);
//       return;
//     }

//     console.log("deleted customer with id: ", id);
//     result(null, res);
//   });
// };

// //delete todas as policials do banco de dados.
// Customer.removeAll = result => {
//   sql.query("DELETE FROM customers", (err, res) => {
//     if (err) {
//       console.log("error: ", err);
//       result(null, err);
//       return;
//     }

//     console.log(`deleted ${res.affectedRows} customers`);
//     result(null, res);
//   });
// };

//buscar uma unica policial pelo id
policial.todospm_unidade = (json, result) => {
  sql.query(`SELECT * FROM db_app_aiba.z_pmba_cad_pm where unidade = (SELECT unidade FROM db_app_aiba.z_pmba_cad_pm WHERE z_pmba_cad_pm.matricula = "${json.id}") order by id;`, (err, res) => {
    //se ocorrer um erro
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca do policial suceder
    if (res.length) {
      console.log("policiais encontrados: ", res);
      result(null, res);
      return;
    }

    //quando não encontrado o policial pelo id
    result({ kind: "não encontrados" }, null);
  });
};

//enviar email para o pm
// const mail_send = require("../../server.js");

// const model_email = function (email) {
//   this.email = email.email;
//   this.texto = email.texto;
// };

// model_email.fire_email = (email, result) => {
//     mail_send.send_mail(email.email, email.texto);
//     result(null, {"response": "OK"});
// };

// //enviar email para o pm coordenador
// const mail_send = require("../../server.js");

// const model_email = function (email) {
//   this.email = email.email;
//   this.texto = email.texto;
// };

// model_email.fire_email = (email, result) => {
//     mail_send.send_mail(email.email, email.texto);
//     result(null, {"response": "OK"});
// };

module.exports = policial;

