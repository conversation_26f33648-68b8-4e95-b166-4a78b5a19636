<configuration>
    <system.webServer>

        <handlers>
            <!-- indicates that the app.js file is a node.js application to be handled by the iisnode module -->
            <add name="iisnode" path="server.js" verb="*" modules="iisnode" />
        </handlers>

        <rewrite>
            <rules>
                <!-- Don't interfere with requests for node-inspector debugging -->
                <rule name="NodeInspector" patternSyntax="ECMAScript" stopProcessing="true">
                    <match url="^server.js\/debug[\/]?" />
                </rule>

                <!-- First we consider whether the incoming URL matches a physical file in the /public folder -->
                <rule name="StaticContent">
                    <action type="Rewrite" url="public{REQUEST_URI}" />
                </rule>

                <!-- All other URLs are mapped to the Node.js application entry point -->
                <rule name="DynamicContent">
                    <conditions>
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="True" />
                    </conditions>
                    <action type="Rewrite" url="server.js" />
                </rule>
            </rules>
        </rewrite>

        <!-- You can control how Node is hosted within IIS using the following options -->
                    <!--<iisnode      
                      node_env="%node_env%"
                      nodeProcessCountPerApplication="1"
                      maxConcurrentRequestsPerProcess="1024"
                      maxNamedPipeConnectionRetry="3"
                      namedPipeConnectionRetryDelay="2000"      
                      maxNamedPipeConnectionPoolSize="512"
                      maxNamedPipePooledConnectionAge="30000"
                      asyncCompletionThreadCount="0"
                      initialRequestBufferSize="4096"
                      maxRequestBufferSize="65536"
                      watchedFiles="*.js"
                      uncFileChangesPollingInterval="5000"      
                      gracefulShutdownTimeout="60000"
                      loggingEnabled="true"
                      logDirectoryNameSuffix="logs"
                      debuggingEnabled="true"
                      debuggerPortRange="5058-6058"
                      debuggerPathSegment="debug"
                      maxLogFileSizeInKB="128"
                      appendToExistingLog="false"
                      logFileFlushInterval="5000"
                      devErrorsEnabled="true"
                      flushResponse="false"      
                      enableXFF="false"
                      promoteServerVars=""
                     />-->
        <iisnode watchedFiles="*.js;node_modules\*;routes\*.js;views\*.jade;views\account\*.jade;iisnode.yml" />
    </system.webServer>
</configuration>