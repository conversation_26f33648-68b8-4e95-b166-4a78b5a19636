const sql = require("./db.js");


const informacao = function (informacao) {
  this.id_prop = informacao.id_prop;
  this.motivo = informacao.motivo;
  this.descricao = informacao.descricao;
  this.onde_registrou = informacao.onde_registrou;
  this.dia = informacao.dia;
  this.hora = informacao.hora;

};

//salvar informacao
informacao.salvar_informacao = (nova_informacao, result) => {
  sql.query("INSERT INTO z_informacoes SET ?", nova_informacao, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    console.log("informacao criada: ", { id: res.insertId, ...nova_informacao });
    result(null, { id: res.insertId, ...nova_informacao });
  });
};

//buscar uma unica informacao pelo id
informacao.buscar_informacao = (json, result) => {
  sql.query(`SELECT * FROM z_informacoes WHERE id = ${json.id}`, (err, res) => {
    //se ocorrer um erro
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca da informacao suceder
    if (res.length) {
      console.log("informacao encontrada: ", res[0]);
      result(null, res[0]);
      return;
    }

    //quando não encontrada a informacao pelo id
    result({ kind: "not_found" }, null);
  });
};

//buscar as informacoes relacionadas ao proprietario pelo id proprietario
informacao.buscar_informacao_proprietario = (json, result) => {
  sql.query(`SELECT * FROM db_app_aiba.z_informacoes WHERE id_prop = '${json.id_prop}'`, (err, res) => {
    //se ocorrer um erro
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca da informacao suceder
    if (res.length) {
      console.log("informacao(s) encontrada(s): ", res);
      result(null, res);
      return;
    }

    //quando não encontrada a informacao pelo id
    result({ kind: "not_found" }, null);
  });
};

//buscar uma unica informacao pelo id
informacao.buscar_informacoes = (json, result) => {
  sql.query(`SELECT * FROM db_app_aiba.z_informacoes_cadastradas where z_informacoes.id  = ${json.id}`, (err, res) => {
    //se ocorrer um erro
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca da informacao suceder
    if (res.length) {
      console.log("informacao encontrada: ", res);
      result(null, res);
      return;
    }

    //quando não encontrada a informacao pelo id
    result({ kind: "not_found" }, null);
  });
};

//buscar todas as informacoes cadastradas
informacao.todas_informacoes = result => {
  sql.query("SELECT * FROM motivo_informacao;", (err, res) => {
    if (err) {
      //se ocorrer um erro
      console.log("error: ", err);
      result(null, err);
      return;
    }
    //se a busca de todas as informacoes suceder 
    console.log("informacoes: ", res);
    result(null, res);
  });
};

//atualiza os dados de uma informacao pelo seu id
informacao.atualizar_informacao = (informacao, result) => {
  console.log(informacao)
  sql.query(
    "UPDATE z_informacoes SET id_prop = ?, motivo = ?, descricao = ?, onde_registrou = ?, dia = ?, hora = ? WHERE id = ?",
    [informacao.id_prop, informacao.motivo, informacao.descricao, informacao.onde_registrou, informacao.dia, informacao.hora, informacao.id],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }

      if (res.affectedRows == 0) {
        //não encontrada informacao com o id
        result({ kind: "informacao não encontrada" }, null);
        return;
      }

      result(null, "OK" );
    }
  );
};

//delete uma informacao com o id especificado na chamada
informacao.deletar_informacao = (id, result) => {

  sql.query("DELETE FROM `db_app_aiba`.`z_informacoes` WHERE z_informacoes.id = ?;", id, (err, res) => {
    if (err) {
      result(err, null);
      return;
    }

    if (res.affectedRows == 0) {
      // not found informacao with the id
      result({ kind: "not_found" }, null);
      return;
    }

    console.log("deleted informacao with id: ", id);
    result(null, res);
  });
  
};

//buscar todas as informacoes cadastradas
informacao.motivosinformacao = result => {
  let resposta = [];
  sql.query(`SELECT * FROM motivo_informacao`, (err, res) => {
     if(err){
       result(err, null);
       return;
     }
     if(res.length){
       res.map(res => {
         const objeto = {
           id: res.id,
           descricao: res.descricao,
         }
         resposta.push(objeto)
       })
       result(null, resposta);
       return
     }
 
     result(null, {
       "message" : "Não há motivos cadastrados para informação"
     })
     return;
   })

}



//insere uma informacao com o id especificado na chamada
informacao.deletar_informacao = (id, result) => {

  sql.query("DELETE FROM `db_app_aiba`.`z_informacoes` WHERE z_informacoes.id = ?;", id, (err, res) => {
    if (err) {
      result(err, null);
      return;
    }

    if (res.affectedRows == 0) {
      // not found informacao with the id
      result({ kind: "not_found" }, null);
      return;
    }

    console.log("deleted informacao with id: ", id);
    result(null, res);
  });
};


//salvar informacao
informacao.abririnformacao = (json, result) => {
  sql.query(`INSERT INTO informacoes (id_prop, id_fzd, id_motivo, registrado_fora_delegacia, informacao) VALUES (${json.id_prop}, ${json.id_fzd}, ${json.id_motivo}, ${json.registrado_fora_delegacia},'${json.descricao}')`,
  (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    console.log("informacao criada: ", { id: res.insertId, ...json });
    result(null, { id: res.insertId, ...json });
  });
}

//buscar todas as informacoes cadastradas ordenadas pelas mais recentes
informacao.informacoes = result => {
  sql.query("SELECT * FROM informacoes order by id desc;", (err, res) => {
    if (err) {
      //se ocorrer um erro
      console.log("error: ", err);
      result(null, err);
      return;
    }
    //se a busca de todas as informacoes suceder
    console.log("informacoes: ", res);
    result(null, res);
  });
};

module.exports = informacao;
