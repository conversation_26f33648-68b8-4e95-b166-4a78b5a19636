const viatura = require("../models/viaturas.model.js");
const viatura_model = require("../models/viaturas.model.js");



// Cria e salva uma nova viatura
exports.salvarviatura = (req, res) => {
  
 
  if (!req.body) {
    res.status(400).send({
      message: "Content can not be empty!"
     
    });

    return;
  }

  // Cria uma viatura

  const viatura = new viatura_model({
    modelo_viat: req.body.modelo_viat,
    placa: req.body.placa,
    km_inicial: req.body.km_inicial,
    tipo_combustivel: req.body.tipo_combustivel,
    unidade: req.body.unidade,
    situacao_vtr: req.body.situacao_vtr,
    setor: req.body.setor,
    area_atuacao: req.body.area_atuacao,
    imagem_viat: req.body.imagem_viat,
    ativa_inativa: req.body.ativa_inativa,
    componentes_viatura: req.body.componentes_viatura
  });

  // Salva viatura no banco de dados
  viatura_model.salvar_viatura(viatura, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Algum erro ocorreu ao criar a viatura."
      });
    else res.send(data);
  });

};

// Busca uma unica viatura pelo seu id
exports.buscarviatura = (req, res) => {
  // console.log(req.body)
  viatura.buscar_viatura(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrada viatura com id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar viatura com id " + req.body
        });
      }
    } else res.send(data);
  });
};


// Busca viaturas pela area de atuação
exports.buscarviatura_por_area = (req, res) => {
  viatura.buscar_viatura_por_area(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrada viatura com id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar viatura com id " + req.body
        });
      }
    } else res.send(data);
  });
};


//mostra todas as viaturas cadastradas
exports.todasviaturas = (req, res) => {
  viatura.todas_viaturas((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Algum erro ocorreu ao tentar recuperar viaturas"
      });
    else res.send(data);
  });
};

//atualiza os dados de uma viatura identificada pelo id na chamada
exports.atualizarviatura = (req, res) => {
  //validar solicitação
  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"
    });
  }

  console.log(req.body);
//verificando se há erros na busca ou na atualização para se não, retornar os dados atualizados
  viatura.atualizar_viatura(req.body, (err, data) => {
      if (err) {
        if (err.kind === "not_found") {
          res.status(404).send({
            message: `Não encontrada viatura com id ${req.params.atualizarviatura}.`
          });
        } else {
          res.status(500).send({
            message: "erro ao atualizar viatura com id " + req.params.atualizarviatura
          });
        }
      } else res.send(data);
    }
  );
};

// //delete uma viatura com o id especificado na chamada
// exports.delete = (req, res) => {
//   Customer.remove(req.params.customerId, (err, data) => {
//     if (err) {
//       if (err.kind === "not_found") {
//         res.status(404).send({
//           message: `Not found Customer with id ${req.params.customerId}.`
//         });
//       } else {
//         res.status(500).send({
//           message: "Could not delete Customer with id " + req.params.customerId
//         });
//       }
//     } else res.send({ message: `Customer was deleted successfully!` });
//   });
// };


// //delete todas as viaturas do banco de dados.
// exports.deleteAll = (req, res) => {
//   Customer.removeAll((err, data) => {
//     if (err)
//       res.status(500).send({
//         message:
//           err.message || "Some error occurred while removing all customers."
//       });
//     else res.send({ message: `All Customers were deleted successfully!` });
//   });
// };

// Busca viaturas pela area de atuação
exports.buscarviatura_por_area = (req, res) => {
  viatura.buscar_viatura_por_area(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrada viatura com id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar viatura com id " + req.body
        });
      }
    } else res.send(data);
  });
};


//seleciona todas as viaturas da unidade pelo ID correspondente ao PM 
exports.todasviaturaunidade = (req, res) => {
  viatura.todas_viatura_unidade(req.body,(err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Algum erro ocorreu ao tentar recuperar viaturas"
      });
    else res.send(data);
  });
}; 