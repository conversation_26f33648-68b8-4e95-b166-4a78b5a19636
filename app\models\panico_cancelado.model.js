const sql = require("./db.js");
const socket = require("../../server.js");

// constructor
const Panico_cancelado = function (panico_cancelado) {
    this.id = panico_cancelado.id;
    this.resumo = panico_cancelado.resumo;
};

Panico_cancelado.disparo = (panico_cancelado, result) => {

    sql.query(`UPDATE db_app_aiba.tbl_panico SET STATUS='3', resumo_panico = '${panico_cancelado.resumo}' WHERE ID = ${panico_cancelado.id}`, (err, res) => {
        if (err) {
            console.log("error: ", err);
            result(null, err);
            return;
        }

        if (res.affectedRows == 0) {
            result({ kind: "not_found" }, null);
            return;
        }
        result(null, {"resposta":"OK"});

        //AVISANDO QUE A OCORRENCIA FOI CANCELADA E DEVOLVENDO O ID DA OCORRENCIA
        socket.panico_socket_cancelado(panico_cancelado.id);
        
    }
    );
};

module.exports = Panico_cancelado;
