const sql = require("./db.js");


const area = function (area) {
  this.nome_da_area = area.nome_da_area;
  this.ponto_de_referencia = area.ponto_de_referencia;
  this.raio = area.raio;
  this.latitude = area.latitude;
  this.longitude = area.longitude;
  this.ativa_inativa = area.ativa_inativa;
};

//salvar area
area.salvar_area = (nova_area, result) => {
  sql.query("INSERT INTO z_admaiba_areas_de_atuacao SET ?", nova_area, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    console.log("area criada: ", { id: res.insertId, ...nova_area });
    result(null, { id: res.insertId, ...nova_area });
  });
};

//buscar uma unica area pelo id
area.buscar_area = (json, result) => {
  sql.query(`SELECT * FROM z_admaiba_areas_de_atuacao WHERE id = ${json.id}`, (err, res) => {
    //se ocorrer um erro
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca da area suceder
    if (res.length) {
      console.log("area encontrada: ", res[0]);
      result(null, res[0]);
      return;
    }

    //quando não encontrada a area pelo id
    result({ kind: "not_found" }, null);
  });
};

//buscar todas as areas cadastradas
area.todas_areas = result => {
  sql.query("select \
                  aa.id \
                  , aa.nome_da_area \
                  , aa.ponto_de_referencia \
                  , aa.raio \
                  , aa.latitude \
                  , aa.longitude \
                  , (select COUNT(cv.area_atuacao) from db_app_aiba.z_pmba_cad_viat cv WHERE cv.area_atuacao = aa.id) as 'Viatura' \
                  , (select COUNT(fc.id_area) from db_app_aiba.z_fazendas_cadastradas fc WHERE fc.id_area = aa.id) as 'Fazenda' \
                from db_app_aiba.z_admaiba_areas_de_atuacao aa \
                  left join db_app_aiba.z_fazendas_cadastradas fc on fc.id_area = aa.id \
                  left join db_app_aiba.z_pmba_cad_viat cv on cv.area_atuacao = aa.id;", (err, res) => {
    if (err) {
      //se ocorrer um erro
      console.log("error: ", err);
      result(null, err);
      return;
    }
    //se a busca de todas as areas suceder 
    console.log("customers: ", res);
    result(null, res);
  });
};

area.atualizar_area = (area, result) => {
  console.log(area)
  sql.query(
    "UPDATE z_admaiba_areas_de_atuacao SET nome_da_area = ?, ponto_de_referencia = ?, raio = ?, latitude = ?, longitude = ?, ativa_inativa = ? WHERE id = ?",
    [area.nome_da_area, area.ponto_de_referencia, area.raio, area.latitude, area.longitude, area.ativa_inativa, area.id],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }

      if (res.affectedRows == 0) {
        //não encontrada area com o id
        result({ kind: "area não encontrada" }, null);
        return;
      }

      result(null, "OK" );
    }
  );
};

// //delete uma fazenda com o id especificado na chamada
// Customer.remove = (id, result) => {
//   sql.query("DELETE FROM customers WHERE id = ?", id, (err, res) => {
//     if (err) {
//       console.log("error: ", err);
//       result(null, err);
//       return;
//     }

//     if (res.affectedRows == 0) {
//       // not found Customer with the id
//       result({ kind: "not_found" }, null);
//       return;
//     }

//     console.log("deleted customer with id: ", id);
//     result(null, res);
//   });
// };

// //delete todas as fazendas do banco de dados.
// Customer.removeAll = result => {
//   sql.query("DELETE FROM customers", (err, res) => {
//     if (err) {
//       console.log("error: ", err);
//       result(null, err);
//       return;
//     }

//     console.log(`deleted ${res.affectedRows} customers`);
//     result(null, res);
//   });
// };

module.exports = area;

