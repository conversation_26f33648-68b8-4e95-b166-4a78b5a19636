const sql = require("./db.js");

//mostra horario
const geolocalizacao = require("../models/salvar_geo.model");
var requestIp = require('request-ip');
let date_ob = new Date();
let date = ("0" + date_ob.getDate()).slice(-2);
let month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
let year = date_ob.getFullYear();
let hours = date_ob.getHours();
let minutes = date_ob.getMinutes(-2);
let seconds = date_ob.getSeconds(-2);
var date_now = year + "-" + month + "-" + date;
var hour_now =  hours + ":" + minutes + ":" + seconds;


const fazenda = function (fazenda) {
  this.nome_da_fazenda = fazenda.nome_da_fazenda;
  this.proprietario = fazenda.proprietario;
  this.id_area = fazenda.id_area;
  this.area_de_operacao = fazenda.area_de_operacao;
  this.municipio = fazenda.municipio;
  this.codigo_da_fazenda = fazenda.codigo_da_fazenda;
  this.latitude = fazenda.latitude;
  this.longitude = fazenda.longitude;
  this.data_do_cadastro = date_now;
  this.hora_do_cadastro = hour_now;
  this.ativo_inativo = fazenda.ativo_inativo;
  this.pernoite = fazenda.pernoite;
  this.combustivel = fazenda.combustivel;

};

//salvar fazenda
fazenda.salvar_fazenda = (nova_fazenda, result) => {
  sql.query("INSERT INTO z_fazendas_cadastradas SET ?", nova_fazenda, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    console.log("Fazenda criada: ", { id: res.insertId, ...nova_fazenda });
    result(null, { id: res.insertId, ...nova_fazenda });
  });
};

//buscar uma unica fazenda pelo id
fazenda.buscar_fazenda = (json, result) => {
  sql.query(`SELECT F.*, P.nome_do_proprietario FROM z_fazendas_cadastradas F JOIN z_proprietarios_cadastrados P ON F.proprietario = P.id_prop WHERE id_fzd = ${json.id_fzd}`, (err, res) => {
    //se ocorrer um erro
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca da fazenda suceder
    if (res.length) {
      console.log("fazenda encontrada: ", res[0]);
      result(null, res[0]);
      return;
    }

    //quando não encontrada a fazenda pelo id
    result({ kind: "not_found" }, null);
  });
};

//buscar extras pelo id fazenda
fazenda.buscar_extras = (json, result) => {
  sql.query(`SELECT pernoite, combustivel FROM db_app_aiba.z_fazendas_cadastradas WHERE id_fzd = ${json.id};`, (err, res) => {
    //se ocorrer um erro
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca da fazenda suceder
    if (res.length) {
      console.log("encontrada: ", res[0]);
      result(null, res[0]);
      return;
    }

    //quando não encontrada a fazenda pelo id
    result({ kind: "não encontrada" }, null);
  });
};

//atualiza os dados dos extras da fazenda pelo seu id
fazenda.atualizar_fazenda = (fazenda, result) => {
  console.log(fazenda)
  sql.query(
    "UPDATE z_fazendas_cadastradas SET pernoite = ?, combustivel = ? WHERE id_fzd = ?;",
    [fazenda.pernoite, fazenda.combustivel, fazenda.id_fzd],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }

      if (res.affectedRows == 0) {
        //não encontrada fazenda com o id
        result({ kind: "fazenda não encontrada" }, null);
        return;
      }

      result(null, "OK" );
    }
  );
};

//buscar as fazendas relacionadas ao proprietario pelo id proprietario
fazenda.buscar_fazenda_proprietario = (json, result) => {
  sql.query(`SELECT * FROM db_app_aiba.api_user WHERE ID_USUARIO = '${json.id}'`, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    if(res.length){
      sql.query(`SELECT * from db_app_aiba.z_proprietarios_cadastrados WHERE email = '${res[0].EMAIL}'`, (err, resProp) => {
        if (err) {
          console.log("error: ", err);
          result(err, null);
          return;
        }
        if(res.length){
          proprietarios = resProp[0]
          console.log(proprietarios.id_prop.toString())
          sql.query(`SELECT * FROM db_app_aiba.z_fazendas_cadastradas WHERE proprietario = ${resProp[0].id_prop}`, (err, resFaz) => {
            if (err) {
              console.log("error: ", err);
              result(err, null);
              return;
            }
            if (res.length) {
              fazendas = resFaz;
              result(null, resFaz);
              return;
            }            
          });
        }
      })
    }
    

    
  })
  
};

//buscar uma unica fazenda pelo id area
fazenda.buscar_fazendas = (json, result) => {
  sql.query(`SELECT * FROM db_app_aiba.z_fazendas_cadastradas where z_fazendas_cadastradas.id_area  = ${json.id}`, (err, res) => {
    //se ocorrer um erro
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca da fazenda suceder
    if (res.length) {
      console.log("fazenda encontrada: ", res);
      result(null, res);
      return;
    }

    //quando não encontrada a fazenda pelo id
    result({ kind: "not_found" }, null);
  });
};

//buscar todas as fazendas cadastradas..
fazenda.todas_fazendas = result => {
  sql.query(`select * from z_fazendas_cadastradas fazendas INNER JOIN z_proprietarios_cadastrados proprietarios on fazendas.proprietario = proprietarios.id_prop`, (err, res) => {
      // sql.query(`select count(*) from z_visitas_validacao where fzd_id = ${teste.id_fzd}`, (err, resposta) => {
      //   return fazendas_proprietarios_visitas.push({
      //     id_fzd: teste.id_fzd,
      //     nome_da_fazenda: teste.nome_da_fazenda,
      //     id_area: teste.id_area,
      //     municipio: teste.municipio,
      //     codigo_da_fazenda: teste.codigo_da_fazenda,
      //     latitude: teste.latitude,
      //     longitude: teste.longitude,
      //     data_do_cadastro: teste.data_do_cadastro,
      //     hora_do_cadastro: teste.hora_do_cadastro,
      //     ativo_inativo: teste.ativo_inativo,
      //     pernoite: teste.pernoite,
      //     combustivel: teste.combustivel,
      //     id_prop: teste.id_prop,
      //     nome_do_proprietario: teste.nome_do_proprietario,
      //     cpf: teste.cpf,
      //     email: teste.email,
      //     telefone: teste.telefone,
      //     data_de_nascimento: teste.data_de_nascimento,
      //     foto_do_proprietario: teste.foto_do_proprietario,
      //     visitas: resposta[0]
      //   })
      // })
      result(null, res)
  })
};

fazenda.visitas_fazendas = (result) => {
  sql.query(`select fzd_id, count(fzd_id) as qtd_visitas from z_visitas_validacao group by fzd_id`, (err, res) => {
    result(null, res)
  })
}

//atualiza os dados de uma fazenda pelo seu id
fazenda.atualizar_fazenda = (fazenda, result) => {
  console.log(fazenda)
  sql.query(
    "UPDATE z_fazendas_cadastradas SET nome_da_fazenda = ?, proprietario = ?, id_area = ?, area_de_operacao = ?, municipio = ?, codigo_da_fazenda = ?, latitude = ?, longitude = ?, data_do_cadastro = ?, hora_do_cadastro = ?, ativo_inativo = ?, pernoite = ?, combustivel = ? WHERE id_fzd = ?",
    [fazenda.nome_da_fazenda, fazenda.proprietario, fazenda.id_area, fazenda.area_de_operacao, fazenda.municipio, fazenda.codigo_da_fazenda, fazenda.latitude, fazenda.longitude, fazenda.data_do_cadastro, fazenda.hora_do_cadastro, fazenda.ativo_inativo, fazenda.pernoite, fazenda.combustivel, fazenda.id_fzd],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }

      if (res.affectedRows == 0) {
        //não encontrada fazenda com o id
        result({ kind: "fazenda não encontrada" }, null);
        return;
      }

      result(null, "OK" );
    }
  );
};

//delete uma fazenda com o id especificado na chamada
fazenda.deletar_fazenda = (id, result) => {

  sql.query("DELETE FROM `db_app_aiba`.`z_fazendas_cadastradas` WHERE z_fazendas_cadastradas.id_fzd = ?;", id, (err, res) => {
    if (err) {
      result(err, null);
      return;
    }

    if (res.affectedRows == 0) {
      // not found Fazenda with the id
      result({ kind: "not_found" }, null);
      return;
    }

    console.log("deleted Fazenda with id: ", id);
    result(null, res);
  });
  
};


module.exports = fazenda;


