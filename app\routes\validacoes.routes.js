module.exports = app => {
  
    const validacoes = require("../controllers/validacoes.controller.js");
  
    //rota para salvar uma nova validacao 
    app.post("/salvar_validacao", validacoes.salvarvalidacao)

    //rota para buscar uma validacao pelo id
    app.post("/buscar_validacao", validacoes.buscarvalidacao)

    //rota para buscar validacoes de um proprietario pelo id
    app.post("/buscar_validacao_proprietario", validacoes.buscarvalidacaoproprietario)

    //rota para buscar validacoes pelo id da area de atuação
    app.post("/buscar_validacoes_atuacao", validacoes.buscarvalidacoes)

    //rota para retornar todas as validacoes
    app.get("/todas_validacoes", validacoes.todasvalidacoes)

    //atualizar os dados de uma validacao a partir do seu id
    app.put("/atualizar_validacao", validacoes.atualizarvalidacao)  
    
    // Delete a Customer with customerId
    app.delete("/deletar_validacao", validacoes.deletarvalidacao);

 
  }; 
  