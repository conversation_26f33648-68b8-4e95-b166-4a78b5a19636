const sql = require("./db.js");
const viatura = function (viatura) {
  this.modelo_viat = viatura.modelo_viat;
  this.placa = viatura.placa;
  this.km_inicial = viatura.km_inicial;
  this.tipo_combustivel = viatura.tipo_combustivel;
  this.unidade = viatura.unidade;
  this.situacao_vtr = viatura.situacao_vtr;
  this.setor = viatura.setor;
  this.area_atuacao = viatura.area_atuacao;
  this.imagem_viat = viatura.imagem_viat;
  this.ativa_inativa = viatura.ativa_inativa;
  this.componentes_viatura = viatura.componentes_viatura;

};

//salvar viatura
viatura.salvar_viatura = (nova_viatura, result) => {
  sql.query("INSERT INTO z_pmba_cad_viat SET ?", nova_viatura, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    console.log("viatura criada: ", { id: res.insertId, ...nova_viatura });
    result(null, { id: res.insertId, ...nova_viatura });
  });
};

//buscar viaturas pela area de atuação
viatura.buscar_viatura_por_area = (json, result) => {
  sql.query(`SELECT * FROM db_app_aiba.z_pmba_cad_viat where area_atuacao = ${json.id}`, (err, res) => {
    //se ocorrer um erro
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca da viatura suceder
    if (res.length) {
      console.log("viatura encontrada: ", res);
      result(null, res);
      return;
    }

    //quando não encontrada a viatura pelo id
    result({ kind: "not_found" }, null);
  });
};


//buscar uma unica viatura pelo id
viatura.buscar_viatura = (json, result) => {
  sql.query(`SELECT * FROM z_pmba_cad_viat WHERE id_viat = ${json.id_viat}`, (err, res) => {
    //se ocorrer um erro
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca da viatura suceder
    if (res.length) {
      console.log("viatura encontrada: ", res[0]);
      result(null, res[0]);
      return;
    }

    //quando não encontrada a viatura pelo id
    result({ kind: "not_found" }, null);
  });
};

//buscar todas as viaturas cadastradas
viatura.todas_viaturas = result => {
  sql.query("SELECT * FROM z_pmba_cad_viat;", (err, res) => {
    if (err) {
      //se ocorrer um erro
      console.log("error: ", err);
      result(null, err);
      return;
    }
    //se a busca de todas as viaturas suceder 
    console.log("viaturas: ", res);
    result(null, res);
  });
};



//atualiza os dados de uma viatura pelo seu id
viatura.atualizar_viatura = (viatura, result) => {
  console.log(viatura)
  sql.query(
    "UPDATE z_pmba_cad_viat SET modelo_viat = ?, placa = ?, km_inicial = ?, tipo_combustivel = ?, situacao_vtr = ?, setor = ?, area_atuacao = ?, imagem_viat = ?, ativa_inativa = ?, unidade = ? WHERE id_viat = ?",
    [viatura.modelo_viat, viatura.placa, viatura.km_inicial, viatura.tipo_combustivel, viatura.situacao_vtr, viatura.setor, viatura.area_atuacao, viatura.imagem_viat, viatura.ativa_inativa, viatura.unidade, viatura.id_viat],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }

      if (res.affectedRows == 0) {
        //não encontrada viatura com o id
        result({ kind: "viatura não encontrada" }, null);
        return;
      }

      result(null, "OK" );
    }
  );
};

// //delete uma viatura com o id especificado na chamada
// Customer.remove = (id, result) => {
//   sql.query("DELETE FROM customers WHERE id = ?", id, (err, res) => {
//     if (err) {
//       console.log("error: ", err);
//       result(null, err);
//       return;
//     }

//     if (res.affectedRows == 0) {
//       // not found Customer with the id
//       result({ kind: "not_found" }, null);
//       return;
//     }

//     console.log("deleted customer with id: ", id);
//     result(null, res);
//   });
// };

// //delete todas as viaturas do banco de dados.
// Customer.removeAll = result => {
//   sql.query("DELETE FROM customers", (err, res) => {
//     if (err) {
//       console.log("error: ", err);
//       result(null, err);
//       return;
//     }

//     console.log(`deleted ${res.affectedRows} customers`);
//     result(null, res);
//   });
// };

viatura.todas_viatura_unidade = (json, result) => {
  console.log(json);
  sql.query(`SELECT * FROM db_app_aiba.z_pmba_cad_pm WHERE z_pmba_cad_pm.matricula = '${json.id}';`, (err, res) => {
    if (err) {
      //se ocorrer um erro
      console.log("error: ", err);
      result(null, err);
      return;
    }
    if(res.length == 0){
      result({kind: "not found"}, null);
      return;
    }
    sql.query(`SELECT * FROM db_app_aiba.z_pmba_cad_viat WHERE z_pmba_cad_viat.unidade = "${res[0].unidade}"`, (err, res) => {
      if(err){
        result(null, err);
        return;
      }
      if(res.length == 0){
        result({kind: "not found"}, null);
        return;
      }
      result(null, res);
      return;
    }) 
    
  });
  // sql.query(`SELECT V.* FROM db_app_aiba.z_pmba_cad_viat V JOIN db_app_aiba.z_pmba_cad_pm P ON V.unidade = P.unidade where P.id = ${json.id}`, (err, res) => {
  //   //se ocorrer um erro
  //   if (err) {
  //     console.log("error: ", err);
  //     result(err, null);
  //     return;
  //   }
  //   //se a busca das viatura suceder
  //   if (res.length) {
  //     console.log("viatura encontrada: ", res);
  //     result(null, res);
  //     return;
  //   }

  //   //quando não encontrada a viatura pelo id
  //   result({ kind: "not_found" }, null);
  // }); 

  
  
};


module.exports = viatura;

