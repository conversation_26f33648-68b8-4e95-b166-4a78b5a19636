var http = require("http");
var path = require("path");
var express = require("express");
const cors = require('cors');
require("./Date.prototype.dateAdd");
var bodyParser = require('body-parser')
const nodemailer = require("nodemailer");
const swaggerUi = require('swagger-ui-express')
const swaggerFile = require('./swagger_output.json')
require('dotenv').config()

const PORT = process.env.PORT || 12103;
process.env['NODE_TLS_REJECT_UNAUTHORIZED'] = 0

var app = express();

// Configure CORS - Temporary permissive for debugging
const corsOptions = {
  origin: function (origin, callback) {
    // Log all origins for debugging
    console.log('CORS request from origin:', origin);

    // Allow all origins temporarily for debugging
    callback(null, true);
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
  preflightContinue: false,
  optionsSuccessStatus: 200
};

app.use(cors(corsOptions));

var server = http.createServer(app);

const io = require('socket.io')(server, {
  cors: {
    origin: function (origin, callback) {
      // Log all Socket.IO origins for debugging
      console.log('Socket.IO request from origin:', origin);

      // Allow all origins temporarily for debugging
      callback(null, true);
    },
    credentials: true,
    methods: ['GET', 'POST']
  }
});

//Remoto IP
app.set('trust proxy', true);
var get_ip = require('ipware')().get_ip;
const socketIO = require('socket.io');
app.use(bodyParser.urlencoded({
  extended: true
}));

app.use(bodyParser.json());


//Preferencialmente adicione as rotas em arquivos separados
require("./app/routes/customer.routes.js")(app);
require("./app/routes/autentication.routes.js")(app);
require("./app/routes/fazendas.routes.js")(app);
require("./app/routes/panico.routes.js")(app);
require("./app/routes/proprietarios.routes.js")(app);
require("./app/routes/viaturas.routes.js")(app);
require("./app/routes/policiais.routes.js")(app);
require("./app/routes/area_atuacao.routes.js")(app);
require("./app/routes/qrcode_gen.routes.js")(app);
require("./app/routes/log_visita_fazenda.routes.js")(app);
require("./app/routes/rotas.routes.js")(app);
require("./app/routes/geolocalizacao.routes")(app);
require("./app/routes/ocorrencias.routes")(app);
require("./app/routes/informacoes.routes")(app);
require("./app/routes/iniciacao.routes")(app);
require("./app/routes/validacoes.routes")(app);
require("./app/routes/user_routes")(app);
require("./app/routes/send_mail.routes")(app);
///////////////////////////////////////////////////////////////////
// Rotas da API - EXPRESS
// CRUD + Oper.
///////////////////////////////////////////////////////////////////
app.get("/", (req, res) => {
  res.json({ message: "Welcome to my testing." });
});


///////////////////////////////////////////////////////////////////
//  Rotas e estados - SOCKET IO
//  Rotas do SOCKET na API - EXPRESS
///////////////////////////////////////////////////////////////////
io.sockets.on("connection", function (socket) {

  // socket.on("connection", function (data) {
  //   console.log("server.updatetime:" + data.second);
  //   console.log("user conectado" + data.second);
  //   // socket.emit("playCourse ::::::");///
  // });

  socket.on('disconnect', function () {
    console.log('A user disconnected');
  });

  console.log("user conectado");

});

// app.post('/', function(req, res) {
//   io.emit('panico', {})
// });

// io.on('connection', function(socket) {
//   console.log('A user connected');
//   io.sockets.emit('hello');

//   //Whenever someone disconnects this piece of code executed
//   socket.on('disconnect', function () {
//      console.log('A user disconnected');
//   });
// });

exports.panico_socket = async (data) => {
  console.log(">>>>>>>>>>>>>>>>>Panico acionado<<<<<<<<<<<<<<<<<<<");//..
  io.emit("Notify", data)
};

// exports.panico_socket = async (data) => {
//   await io.sockets.emit('panico', data);
//   console.log("panico disparado");//..
// };

exports.panico_socket_acaminho = async (data) => {
  await io.sockets.emit('Atenção viatura a caminho', { "Aviso": "acaminho", "ID": data })
  console.log("panico acaminho");//..
};

exports.panico_socket_cancelado = async (data) => {
  await io.sockets.emit('Atenção ocorrencia cancelada', { "Aviso": "cancelada", "ID": data })
  console.log("panico cancelado");//..
};

exports.panico_socket_finalizado = async (data) => {
  await io.sockets.emit('Atenção ocorrencia finalizada', { "Aviso": "Finalizada", "ID": data })
  console.log("panico finalizado");//..
};

var staticPath = path.resolve(__dirname, "");
app.use(express.static(staticPath));
app.use('/doc', swaggerUi.serve, swaggerUi.setup(swaggerFile))

// Health check endpoint for Docker
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'OK', timestamp: new Date().toISOString() });
});

server.listen(PORT, function () {
  console.log("Server is listening at http://localhost:" + PORT);
});

// function tick() {
//   var dt = new Date();
//   dt = dt.toUTCString();
//   io.sockets.send("Conectado agora"); //
// }..

exports.send_mail = async (mail, text, opcao) => {
  // const proprietariomail = ""
  // sql.query(`SELECT email FROM z_proprietarios_cadastrados WHERE id_prop = ${json.id_prop}`, (err, res) => {
  //   //se ocorrer um erro
  //   if (err) {
  //     console.log("error: ", err);
  //     result(err, null);
  //     return;
  //   }
  //   //se a busca do proprietario suceder
  //   if (res.length) {
  //     proprietariomail = res[0]['email']
  //     return;
  //   }

  //   //quando não encontrado o proprietario pelo id
  //   result({ kind: "not_found" }, null);
  // });

  let testAccount = await nodemailer.createTestAccount();
  testAccount.user = "<EMAIL>"
  testAccount.pass = "**************************************************"

  // create reusable transporter object using the default SMTP transport
  let transporter = nodemailer.createTransport({
    host: "smtp.mailgun.org",
    port: 587,
    secure: false, // true for 465, false for other ports
    auth: {
      user: testAccount.user, // generated ethereal user
      pass: testAccount.pass, // generated ethereal password
    },
  });

  switch(opcao) {
    case "sendtest":
      text = 'Teste Modelo'
      console.log('email disparado: ' + text)
      break;
    case "pm":
      text = 'Oi Policial'
      console.log('email disparado: ' + text)
    break;
    case "coord":
      text = 'Oi coordenador PMBA'
      console.log('email disparado: ' + text)
    break;
    case "proprietario":
      text = 'Oi Proprietario da fazenda'
      console.log('email disparado: ' + text)
      break;
    default:
      // code block
  }

  // send mail with defined transport object
  let info = await transporter.sendMail({
    from: '<EMAIL>', 
    to: mail, 
    subject: "Hello ✔", 
    text: text, 
    html: "<b>Hello world?</b>", 
  }).then(message => {
    console.log(message); //tratamento necessário para o transporter funcionar, não remover
  }).catch(err => {
    console.log(err); //tratamento necessário para o transporter funcionar, não remover
  })

  
  // console.log("Message sent: %s", info.messageId);
  // // Message sent: <<EMAIL>>

  // // Preview only available when sending through an Ethereal account
  // console.log("Preview URL: %s", nodemailer.getTestMessageUrl(info));
  // // Preview URL: https://ethereal.email/message/WaQKMgKddxQDoou...
};

module.exports = socketIO(io);