const sql = require("./db.js");


const User = function (user) {
  this.ID_USUARIO = user.ID_USUARIO;
  this.ID_FAZENDA = user.ID_FAZENDA;
  this.NOME = user.NOME;
  this.USUARIO = user.USUARIO;
  this.SENHA = user.SENHA;
  this.EMAIL = user.EMAIL;
  this.TELEFONE = user.TELEFONE;
  this.PERMISSAO = user.PERMISSAO;
  this.STATUS = user.STATUS;
  this.IDENTIFICACAO = user.IDENTIFICACAO;
};

//salvar validacao
User.insert_user = (user, result) => {
  sql.query("INSERT INTO db_app_aiba.api_user SET ?", user, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    result(null, { id: res.insertId, ...user });

  });
};





module.exports = User;


