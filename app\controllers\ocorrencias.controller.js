const ocorrencia = require("../models/ocorrencias.model.js");
const ocorrencia_model = require("../models/ocorrencias.model.js");



// Cria e salva uma nova ocorrencia

exports.get_motivos_ocorrencia = (req, res) => {
  ocorrencia_model.motivos_ocorrencia((err, data) => {
    if(err){
      res.status(500).send({
        message: 
          err.message || "Ocorreu algum problema ao buscar os motivos da ocorrência"
      })
    }
    else{
      res.send(data)
    }
  })
}

exports.insert_registrar_ocorrencia = (req, res) => {
  if(!req.body.id_prop){
    res.status(400).send({
      message: "id_prop não pode ser nulo"
    })
    return;
  }

  if(!req.body.id_fzd){
    res.status(400).send({
      message: "id_fzd não pode ser nulo"
    })
    return;
  }

  if(!req.body.id_motivo || req.body.id_motivo == ''){
    res.status(400).send({
      message: "id_motivo não pode ser nulo"
    })
    return;
  }

  if(!req.body.descricao){
    res.status(400).send({
      message: "descricao não pode ser nulo"
    })
    return;
  }

  ocorrencia_model.registrar_ocorrencia(req.body, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Ocorreu algum erro ao criar ocorrencia."
      });
    else res.send(data);
  });
}

exports.get_ocorrencias = (req, res) => {
  if(req.query.status === ''){
    res.status(400).send({
      message: "status não pode ser nulo"
    })
    return;
  }
  ocorrencia_model.buscar_ocorrencia(req.query, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Ocorreu algum erro ao criar ocorrencia."
      });
    else res.status(200).send(data);
  });
}

exports.enviar_viatura = (req, res) => {
  if(!req.body.id_viat){
    res.status(400).send({
      message: "id_viat não pode ser nulo"
    })
    return;
  }

  if(!req.body.id_ocorrencia){
    res.status(400).send({
      message: "id_ocorrencia não pode ser nulo"
    })
    return;
  }

  ocorrencia_model.enviar_viatura(req.body, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Ocorreu algum erro ao enviar viatura."
      });
    else res.status(200).send(data);
  })

}

exports.concluir_ocorrencia = (req, res) => {
  if(!req.body.id_ocorrencia){
    res.status(400).send({
      message: "id_ocorrencia não pode ser nulo"
    })
    return;
  }

  if(!req.body.justificativa){
    res.status(400).send({
      message: "justificativa não pode ser nulo"
    })
    return;
  }

  ocorrencia_model.concluir_ocorrencia(req.body, (err, data) => {
    if (err)
      res.status(500).send({
        errmessage:
          err.message || "Ocorreu algum erro ao concluir a ocorrência."
      });
    else res.status(200).send(data);
  })
}

exports.detalhe_ocorrencia = (req, res) => {
  if(!req.query.id_ocorrencia){
    res.status(400).send({
      message: "id_ocorrencia não pode ser nulo"
    })
    return;
  }

  ocorrencia_model.detalhe_ocorrencia(req.query, (err, data) => {
    if (err)
      res.status(500).send({
        errmessage:
          err.message || "Ocorreu algum erro ao buscar detalhes da ocorrência."
      });
    else res.status(200).send(data);
  })
}

exports.ocorrencias_viatura = (req, res) => {
  if(!req.query.id_viat){
    res.status(400).send({
      message: "id_viat não pode ser nulo"
    })
    return;
  }
  
  ocorrencia_model.ocorrencias_viatura(req.query, (err, data) => {
    if (err)
      res.status(500).send({
        errmessage:
          err.message || "Ocorreu algum erro ao buscar as ocorrências da viatura."
      });
    else res.status(200).send(data);
  })
}

exports.cancelar_ocorrencia = (req, res) => {
  if(!req.body.id_ocorrencia){
    res.status(400).send({
      message: "id_ocorrencia não pode ser nulo"
    })
    return;
  }

  if(!req.body.justificativa){
    res.status(400).send({
      message: "justificativa não pode ser nulo"
    })
    return;
  }

  ocorrencia_model.cancelar_ocorrencia(req.body, (err, data) => {
    if (err)
      res.status(500).send({
        errmessage:
            err.message || "Ocorreu algum erro ao buscar as ocorrências da viatura."
      });
    else res.status(200).send(data);
  })
  
}

exports.ocorrencias_fazenda = (req, res) => {
  if(!req.query.id_fzd){
    res.status(400).send({
      message: "id_fzd não pode ser nulo"
    })
    return;
  }

  ocorrencia_model.ocorrencias_fazenda(req.query, (err, data) => {
    if (err)
      res.status(500).send({
        errmessage:
          err.message || "Ocorreu algum erro ao buscar as ocorrências da fazenda."
      });
    else res.status(200).send(data);
  })
}
