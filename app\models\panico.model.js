const sql = require("./db.js");
const socket = require("../../server");


// constructor
const Panico = function (panico) {
    this.latitude = panico.latitude;
    this.longitude = panico.longitude;
    this.hora = panico.hora;
    this.user_id = panico.user_id;
    this.tipo = panico.tipo;
    this.STATUS = panico.STATUS;
    this.IP = panico.IP;
    this.PROPRIETARIO = panico.PROPRIETARIO;
    this.NOME_FAZENDA = panico.NOME_FAZENDA;
    this.NOME_AREAFZD = panico.NOME_AREAFZD;
    this.VIATURA = panico.VIATURA;

};

Panico.disparo = (newPanico, result) => {

    // console.log(newPanico)///

    let last_id_panico;
    let dados;
    let resut;

    sql.query("INSERT INTO db_app_aiba.tbl_panico SET ?", newPanico, (err, res) => {
        // contrutor_last_id(res.insertId)
        last_id_panico = res.insertId

        sql.query(`SELECT * FROM  api_user WHERE ID_USUARIO = '${newPanico.user_id}' ;`, (err, res) => {

            if (res.affectedRows == 0) {
                return;
            }

            if (res[0].IDENTIFICACAO == "PM") {
                // contrutor_dados(res);
                dados = res;
                console.log("<<<<<<<<<<<<PM>>>>>>>>>>>>" + res)
                resut = {
                    "dados_panico": newPanico,
                    "dados_quem_foi": dados,
                    "last_id": last_id_panico
                };

                socket.panico_socket(resut)
                result(null, { "resposta": "Alerta acionado!", "last_id": last_id_panico });
                
            } else {
                sql.query(`SELECT * FROM  db_app_aiba.z_fazendas_cadastradas WHERE z_fazendas_cadastradas.proprietario = '${newPanico.user_id}' ;`, (err, res) => {
                    // contrutor_dados(res);
                    dados = res;
                    console.log("<<<<<<<<<<<<FZD>>>>>>>>>>>>" + res)
                    resut = {
                        "dados_panico": newPanico,
                        "dados_quem_foi": dados,
                        "last_id": last_id_panico
                    };

                    socket.panico_socket(resut)
                    result(null, { "resposta": "Alerta acionado!", "last_id": last_id_panico });
                });
            }

            // resut = {
            // "dados_panico": newPanico,
            // "dados_quem_foi": dados,
            // "last_id": last_id_panico
            // };


        });
    });
};

//buscar todos as panicos cadastradas
Panico.todos_panicos = result => {
    sql.query("SELECT * FROM db_app_aiba.tbl_panico;", (err, res) => {
        if (err) {
            //se ocorrer um erro
            console.log("error: ", err);
            result(null, err);
            return;
        }
        //se a busca de todas as panicos suceder 
        // console.log("panicos: ", res);
        result(null, res);
    });
};


//buscar todos as panicos cadastradas
Panico.panicos_id = (id, result) => {
    sql.query(`SELECT * FROM db_app_aiba.tbl_panico where tbl_panico.STATUS = '1' and  VIATURA = ${id};`, (err, res) => {
        if (err) {
            //se ocorrer um erro
            console.log("error: ", err);
            result(null, err);
            return;
        }
        result(null, res);
    });
};

Panico.detalhes_panico = (id, result) => {
    let panico;
    let fazenda;
    let proprietario;
    sql.query(`SELECT * FROM db_app_aiba.tbl_panico WHERE tbl_panico.id = ${id};`, (err, res) => {
        if (err) {
            console.log("error", err)
            result(null, err)
            return;
        }
        if (res.length == 0) {
            result(null, `Panico com ID ${id} não encontrado`)
            return;
        }
        panico = res[0];
        sql.query(`SELECT * FROM db_app_aiba.z_fazendas_cadastradas WHERE id_fzd = ${panico.NOME_FAZENDA}`, (err, res) => {
            if (err) {
                console.log("error", err)
                result(null, err)
                return;
            }
            fazenda = res[0];
            sql.query(`SELECT NOME, EMAIL, TELEFONE FROM db_app_aiba.api_user WHERE ID_USUARIO = ${panico.PROPRIETARIO}`, (err, res) => {
                if (err) {
                    console.log("error", err)
                    result(null, err)
                    return;
                }
                proprietario = res[0]
                result(null, {
                    panico: panico,
                    fazenda: fazenda,
                    proprietario: proprietario
                })
            })
        })
    })
    
}

Panico.panico_id_fazenda = (id, result) => {
    sql.query(`SELECT * FROM db_app_aiba.tbl_panico where tbl_panico.NOME_FAZENDA = ${id} AND (tbl_panico.STATUS = 0 OR tbl_panico.status = 1)`, (err, res) => {
        if (err) {
            console.log("error", err)
            result(null, err)
            return;
        }
        if (res.length == 0) {
            result(null, `Não há nenhum pânico ativo na fazenda`)
            return;
        }
        result(null, res)
    })
}

Panico.get_equipment_by_fzd_id = async(id, result) => {
    sql.query(`SELECT * from db_app_aiba.fzd_has_equipment where fzd_has_equipment.fk_id_fzd = ${id}`, (err, res) => {
        if(err){
            console.log("error", err)
            result(null, err)
            return;
        }
        result(null, res[0])
    })
}

module.exports = Panico;
