module.exports = app => {
  
    const iniciacoes = require("../controllers/iniciacao.controller.js");
  
    //rota para salvar uma nova iniciacao 
    app.post("/salvar_iniciacao", iniciacoes.salvariniciacao)

    //rota para buscar uma iniciacao pelo id
    app.post("/buscar_iniciacao", iniciacoes.buscariniciacao)

    //rota para buscar iniciacoes de um proprietario pelo id
    app.post("/buscar_iniciacao_proprietario", iniciacoes.buscariniciacaoproprietario)

    //rota para buscar iniciacoes pelo id da area de atuação
    app.post("/buscar_iniciacoes_atuacao", iniciacoes.buscariniciacoes)

    //rota para retornar todas as iniciacoes
    app.get("/todas_iniciacoes", iniciacoes.todasiniciacoes)

    //atualizar os dados de uma iniciacao a partir do seu id
    app.put("/atualizar_iniciacao", iniciacoes.atualizariniciacao)  
    
    // Delete a Customer with customerId
    app.delete("/deletar_iniciacao", iniciacoes.deletariniciacao);

 
  }; 
  