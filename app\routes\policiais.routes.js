module.exports = app => {
  
    const Policiais = require("../controllers/policiais.controller.js");
  
  
    //rota para salvar um novo policial 
    app.post("/salvar_pm", Policiais.salvarpolicial)

    //rota para buscar uma fazenda pelo id
    app.post("/buscar_policial", Policiais.buscarpolicial)

    //rota para retornar todas as policiais
    app.get("/todos_policiais", Policiais.todospoliciais)

    //atualizar os dados de uma fazenda a partir do seu id
    app.put("/atualizar_policial", Policiais.atualizarpolicial)  
    
    // // Delete a Customer with customerId
    // app.delete("/customers/:customerId", customers.delete);

    //rota para buscar uma fazenda pelo id
    app.post("/todospm_unidade", Policiais.todospmunidade)

    //rota para enviar email para o pm
    // app.post("/send_mail", Email.prepare_email)

    //rota para enviar email para o coordenador
    // app.post("/send_mail", Email.prepare_email)  




  
    

    
    
    
    
  };
  