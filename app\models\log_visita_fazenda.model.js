const sql = require("./db.js");
const log_visita = function (log_visita) {
  this.dia = log_visita.dia;
  this.hora = log_visita.hora;
  this.policial = log_visita.policial;
  this.qrcode = log_visita.qrcode;
  this.geo_pos_leitor = log_visita.geo_pos_leitor;
  this.id_fzd = log_visita.id_fzd;

};

//salvar log_visita
log_visita.salvar_log_visita = (novalog_visita, result) => {
  sql.query("INSERT INTO z_log_visitas_fazendas SET ?", novalog_visita, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    console.log("log_visita criada: ", { id: res.insertId, ...novalog_visita });
    result(null, { id: res.insertId, ...novalog_visita });
  });
};

//buscar uma unica log_visita pelo id
//[`dia`,`hora`,`policial`,`qrcode`,`geo_pos_leitor`,`id_fzd`]
log_visita.buscar_log_visita = (json, result) => {
  sql.query(`SELECT * FROM z_log_visitas_fazendas WHERE id_fzd = ${json.id_fzd}`, (err, res) => {
    //se ocorrer um erro
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca da log_visita suceder
    if (res.length) {
      console.log("log_visita encontrada: ", res[0]);
      result(null, res[0]);
      return;
    }

    //quando não encontrada a log_visita pelo id
    result({ kind: "not_found" }, null);
  });
};

//buscar todas as log_visitas cadastradas
log_visita.todas_log_visitas = result => {
  sql.query("SELECT * FROM z_log_visitas_fazendas;", (err, res) => {
    if (err) {
      //se ocorrer um erro
      console.log("error: ", err);
      result(null, err);
      return;
    }
    //se a busca de todas as log_visitas suceder 
    console.log("log_visitas: ", res);
    result(null, res);
  });
};

//atualiza os dados de uma log_visita pelo seu id
//[`dia`,`hora`,`policial`,`qrcode`,`geo_pos_leitor`,`id_fzd`]
log_visita.atualizar_log_visita = (log_visita, result) => {
  console.log(log_visita)
  sql.query(
    "UPDATE z_log_visitas_fazendas SET dia = ?, hora = ?, policial = ?, qrcode = ?, geo_pos_leitor = ?, id_fzd = ? WHERE id = ?",
    [log_visita.dia, log_visita.hora, log_visita.policial, log_visita.qrcode, log_visita.geo_pos_leitor, log_visita.id_fzd, log_visita.id],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }

      if (res.affectedRows == 0) {
        //não encontrada log_visita com o id
        result({ kind: "log_visita não encontrada" }, null);
        return;
      }

      result(null, "OK" );
    }
  );
};

//delete uma log_visita com o id especificado na chamada
log_visita.deletar_log_visita = (id, result) => {

  sql.query("DELETE FROM `db_app_aiba`.`z_log_visitas_fazendas` WHERE z_log_visitas_fazendas.id = ?;", id, (err, res) => {
    if (err) {
      result(err, null);
      return;
    }

    if (res.affectedRows == 0) {
      // not found log_visita with the id
      result({ kind: "not_found" }, null);
      return;
    }

    console.log("deleted log_visita with id: ", id);
    result(null, res);
  });
  
};


module.exports = log_visita;

