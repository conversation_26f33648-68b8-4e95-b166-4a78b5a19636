const sql = require("./db.js");

let date_ob = new Date();
let date = ("0" + date_ob.getDate()).slice(-2);
let month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
let year = date_ob.getFullYear();
let hours = (date_ob.getHours());
let minutes = date_ob.getMinutes(-2);
let seconds = date_ob.getSeconds(-2);
var date_now = year + "-" + month + "-" + date;
var hour_now = hours + ":" + minutes + ":" + seconds;

const rota = function (rota) {
  this.id = rota.id;
  this.data_criacao = rota.data_criacao;
  this.hora_criacao = rota.hora_criacao;
  this.id_area = rota.id_area;
  this.id_viat = rota.id_viat;
  this.unidade = rota.unidade;
  this.data_inicio = rota.data_inicio;
  this.hora_inicio = rota.hora_inicio;
  this.lista_fazendas = rota.lista_fazendas;
  this.kilometragem_inicial = rota.kilometragem_inicial;
  this.kilometragem_estimada = rota.kilometragem_estimada;
  this.componentes_policiais = rota.componentes_policiais;
  this.responsavel = rota.responsavel;
  this.status = rota.status;
  this.data_encerra = rota.data_encerra;
  this.hora_encerra = rota.hora_encerra;


};

//salvar rota

rota.salvar_rota = (nova_rota, result) => {
  sql.query("INSERT INTO z_rotas SET ?", nova_rota, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    console.log("rota criada: ", { id: res.insertId, ...nova_rota });
    result(null, { id: res.insertId, ...nova_rota });
  });
};

//buscar uma unica rota pelo id
rota.buscar_rota = (json, result) => {

  sql.query(`SELECT * FROM db_app_aiba.z_rotas where id = ${json.id}`, (err, res) => {
    // exibirXml(res[0])
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca da fazenda suceder
    if (res.length) {
      // console.log("viatura encontrada: ", res[0]);
      // result(null, res[0]);
      exibirXml(res[0])
      return;
    }

    //quando não encontrada a fazenda pelo id
    result({ kind: "not_found" }, null);
  });


  function exibirXml(dados_rota) { // '37,52,58,10,85'

    // const fazendas = dados_rota.lista_fazendas
    // console.log(fazendas)
    // sql.query(`select F.*, V.status_visita from db_app_aiba.z_fazendas_cadastradas F INNER JOIN db_app_aiba.z_visitas_validacao V ON F.id_fzd = V.fzd_id WHERE id_fzd IN (${fazendas}) `, (err, res) => {
    //   result(null, { "rota": dados_rota, "fazendas": res });

    //   console.log(dados_rota, res)
    // });
    
    let dados_fazendas;
    let fazendas_visitadas
    let viatura
    let pms

    sql.query(`select * from db_app_aiba.z_fazendas_cadastradas where id_fzd in (${dados_rota.lista_fazendas});`, (err, res) => {
      dados_fazendas = res;

      sql.query(`select * from db_app_aiba.z_visitas_validacao where id_rota = ${dados_rota.id}`, (err, res) => {
        fazendas_visitadas = res;

        sql.query(`select * from db_app_aiba.z_pmba_cad_viat where id_viat = ${dados_rota.id_viat}`, (err, res) => {
          viatura = res;

          if(dados_rota.componentes_policiais !== null){
            sql.query(`select * from db_app_aiba.z_pmba_cad_pm where id in (${dados_rota.componentes_policiais.slice(1, -1)})`, (err, res) => {
              pms = res;
              result(null, 
                { 
                  "rota": dados_rota, 
                  "fazendas": dados_fazendas, 
                  "fazendas_visitadas": fazendas_visitadas,
                  "viatura": viatura,
                  "componentes_policiais": pms
              });
            })
          }else{
            pms = [];
            result(null, 
              { 
                "rota": dados_rota, 
                "fazendas": dados_fazendas, 
                "fazendas_visitadas": fazendas_visitadas,
                "viatura": viatura,
                "componentes_policiais": pms
            });
          }

          
        })
        
        })
      })
      

  }


};

//buscar uma unica fazenda pelo id
rota.detalhe_responsavel = (json, result) => {
  sql.query(`SELECT R.id, R.responsavel, P.nome, R.data_inicio, curtime() 'hora_requisicao' FROM db_app_aiba.z_rotas R JOIN db_app_aiba.z_pmba_cad_pm P ON R.responsavel = P.id WHERE (R.id_viat = ${json.id} AND R.status != 'Encerrada') `, (err, res) => {
    //se ocorrer um erro
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca da fazenda suceder
    if (res.length) {
      console.log("viatura encontrada: ", res[0]);
      result(null, res[0]);
      return;
    }

    //quando não encontrada a fazenda pelo id
    result({ kind: "not_found" }, null);
  });
};

//buscar uma unica fazenda pelo id
rota.detalhe_visita = (json, result) => {
  sql.query(`select visitas.*, rotas.id as id_rotas, rotas.id_viat, rotas.unidade, rotas.responsavel, pm.id, pm.nome, pm.posto_graduacao from z_visitas_validacao as visitas join z_rotas as rotas on visitas.id_rota = rotas.id join z_pmba_cad_pm as pm on rotas.responsavel = pm.id where fzd_id = ${json.id};`, (err, res) => {
    //se ocorrer um erro
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca da fazenda suceder
    if (res.length) {
      console.log("viatura encontrada: ", res);
      result(null, res);
      return;
    }

    //quando não encontrada a fazenda pelo id
    result({ kind: "not_found" }, null);
  });
};

//buscar todas as rotas cadastradas
rota.todas_rotas = (rotaId, result) => {
  console.log(hour_now)
  if(rotaId){
    sql.query(`SELECT R.*, A.nome_da_area FROM db_app_aiba.z_rotas R INNER JOIN db_app_aiba.z_admaiba_areas_de_atuacao A ON R.id_area = A.id WHERE R.id_area = ${rotaId};`, (err, res) => {
      if (err) {
        //se ocorrer um erro
        console.log("error: ", err);
        result(null, err);
        return;
      }
      //se a busca de todas as rotas suceder 
      console.log("rotas: ", res);
      result(null, res);
    });
  }
  else{
    sql.query("SELECT R.*, A.nome_da_area FROM db_app_aiba.z_rotas R INNER JOIN db_app_aiba.z_admaiba_areas_de_atuacao A ON R.id_area = A.id;", (err, res) => {
      if (err) {
        //se ocorrer um erro
        console.log("error: ", err);
        result(null, err);
        return;
      }
      //se a busca de todas as rotas suceder 
      console.log("rotas: ", res);
      result(null, res);
    });
  }
};

//atualiza os dados de uma rota pelo seu id
rota.atualizar_rota = (rota, result) => {
  console.log(rota)
  sql.query(
    "UPDATE z_rotas SET data_criacao = ?, hora_criacao = ?, id_area = ?, lista_fazendas = ?, status = ? WHERE id = ?",
    [rota.data_criacao, hour_now, rota.id_area, rota.lista_fazendas, rota.status, rota.id],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }

      if (res.affectedRows == 0) {
        //não encontrada rota com o id
        result({ kind: "rota não encontrada" }, null);
        return;
      }

      result(null, "OK");
    }
  );
};


//atualiza os dados de uma rota pelo seu id
rota.atualizar_rota_2 = (rota, result) => {
  console.log(rota)
  sql.query(
    "UPDATE z_rotas SET id_viat  = ?, responsavel  = ?, data_inicio  = ?, status  = ? WHERE id = ?",
    [rota.id_viat, rota.responsavel, rota.data_inicio, rota.status, rota.id],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }

      if (res.affectedRows == 0) {
        //não encontrada rota com o id
        result({ kind: "rota não encontrada" }, null);
        return;
      }

      result(null, "OK");
    }
  );
};

//salva a etapa 2 da rota a partir do seu id
// id_viat 
// responsavel : 
// data_inicio : 
// status : 
// id : 
rota.salvar_rota2 = (rota, result) => {
  console.log(rota)
  sql.query(
    "UPDATE `db_app_aiba`.`z_rotas` SET `id_viat` = ?, `data_inicio` = ?, `responsavel` = ?, `status` = ? WHERE `id` = ?",
    [rota.id_viat, rota.data_inicio, rota.responsavel, rota.status, rota.id],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }

      if (res.affectedRows == 0) {
        //não encontrada rota com o id
        result({ kind: "rota não encontrada" }, null);
        return;
      }

      result(null, "OK");
    }
  );
};

//salva a etapa 3 da rota a partir do seu id
rota.salvar_rota3 = (rota, result) => {
  console.log(rota)
  sql.query(
    "UPDATE `db_app_aiba`.`z_rotas` SET `status` = ?, `hora_inicio` = ?, `componentes_policiais` = ?, `kilometragem_inicial` = ? WHERE `id` = ? ;",
    ['iniciada', hour_now, rota.componentes_policiais, rota.kilometragem_inicial, rota.id],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }

      if (res.affectedRows == 0) {
        //não encontrada rota com o id
        result({ kind: "rota não encontrada" }, null);
        return;
      }  
      result(null, "OK");
    }
  );
};

//delete uma rota com o id especificado na chamada
// //aqui vamos as fazendas da rota e incluir na tabela de validação que foram iniciadas SELECT * FROM db_app_aiba.z_rotas where id = 1;
      // sql.query(`SELECT lista_fazendas FROM db_app_aiba.z_rotas where id = ${json.id}`, (err, res) => {
      //   // exibirXml(res[0])
      //   if (err) {
      //     console.log("error: ", err);
      //     result(err, null);
      //     return;
      //   }

      //   if (res.length) {
      //     const fazendas = res[0].lista_fazendas.split(",");

      //     for (let i = 0; i < fazendas.length; i = i + 1) {
      //       sql.query(`INSERT INTO 'db_app_aiba'.'z_visitas_validacao' ("latitude","longitude","status_visita","data_validacao","hora_validacao","id_viatura","viatura","fzd_id")VALUES("","","iniciada","","","","", ${fazendas[0]});`, (err, res) => {
                 
      //       });
      //     }

      //     return;
      //   }

      //   //quando não encontrada a fazenda pelo id
      //   result({ kind: "not_found" }, null);
      // });
//Table: z_rotas, id_area, area_atuacao, id_viat, unidade, data_inicio, lista_fazendas, kilometragem_estimada, componentes_policiais, rota_no_mapa

rota.deletar_rota = (id, result) => {

  sql.query("DELETE FROM `db_app_aiba`.`z_rotas` WHERE z_rotas_cadastradas.id = ?;", id, (err, res) => {
    if (err) {
      result(err, null);
      return;
    }

    if (res.affectedRows == 0) {
      // not found rota with the id
      result({ kind: "not_found" }, null);
      return;
    }

    console.log("deleted rota with id: ", id);
    result(null, res);
  });

};

//rota para buscar uma fazenda pelo id da rota**
rota.buscar_fazenda_rota = (json, result) => {

  sql.query(`SELECT * FROM db_app_aiba.z_rotas where id = ${json.id}`, (err, res) => {
    // exibirXml(res[0])
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca da fazenda suceder
    if (res.length) {
      // console.log("viatura encontrada: ", res[0]);
      // result(null, res[0]);
      exibirXml(res[0])
      return;
    }

    //quando não encontrada a fazenda pelo id
    result({ kind: "not_found" }, null);
  });


  function exibirXml(dados_rota) { // '37,52,58,10,85'

    const fazendas = dados_rota.lista_fazendas;

    let dados_fazendas;
    let fazendas_visitadas

    sql.query(`select * from db_app_aiba.z_fazendas_cadastradas where id_fzd in (${fazendas});`, (err, res) => {
      dados_fazendas = res;
      sql.query(`select * from db_app_aiba.z_visitas_validacao where id_rota = ${dados_rota.id}`, (err, res) => {
        if(res.length) {
          fazendas_visitadas = res;
        }

        result(null, { "rota": dados_rota, "fazendas": dados_fazendas, "fazendas_visitadas": fazendas_visitadas });
      })
      
    })
    
    // sql.query(`select F.*, V.status_visita from db_app_aiba.z_fazendas_cadastradas F INNER JOIN db_app_aiba.z_visitas_validacao V ON F.id_fzd = V.fzd_id WHERE id_fzd IN (${fazendas}) `, (err, res) => {
    //   result(null, { "rota": dados_rota, "fazendas": res });

    //   console.log(res)
    // });
  }


};

//encerra a rota pelo id
rota.encerrar_rota = (json, result) => {
  console.log(rota)
  sql.query(
    `UPDATE z_rotas SET status='Encerrada', pessoas_abordadas = ${json.pessoas_abordadas}, veiculos_4_rodas = ${json.veiculos_4_rodas}, veiculos_2_rodas = ${json.veiculos_2_rodas}, armas_apreendidas = ${json.armas_apreendidas}, apresentacao_na_delegacia = ${json.apresentacao_na_delegacia}, estabelecimento_abordado = ${json.estabelecimento_abordado}, carga_recuperada = ${json.carga_recuperada}, veiculo_recuperado = ${json.veiculo_recuperado}, km_final_viatura = ${json.km_final_viatura} WHERE id = ${json.id} ;`,
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }

      if (res.affectedRows == 0) {
        //não encontrada rota com o id
        result({ kind: "rota não encontrada" }, null);
        return;
      }  
      result(null, "Rota concluida!");
    }
  );
};


//rota para buscar uma fazenda pelo id da rota**
rota.relatorio_rotas = (json, result) => {

  sql.query(`SELECT * FROM db_app_aiba.z_rotas where id = ${json.id}`, (err, res) => {
    // exibirXml(res[0])
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca da fazenda suceder
    if (res.length) {
      // console.log("viatura encontrada: ", res[0]);
      // result(null, res[0]);
      exibirXml(res[0])
      return;
    }

    //quando não encontrada a fazenda pelo id
    result({ kind: "not_found" }, null);
  });


  function exibirXml(dados_rota) { // '37,52,58,10,85'

    const fazendas = dados_rota.lista_fazendas;

    let dados_fazendas;
    let fazendas_visitadas

    sql.query(`select * from db_app_aiba.z_fazendas_cadastradas where id_fzd in (${fazendas});`, (err, res) => {
      dados_fazendas = res;
      sql.query(`select * from db_app_aiba.z_visitas_validacao where id_rota = ${dados_rota.id}`, (err, res) => {
        if(res.length) {
          fazendas_visitadas = res;
        }

        result(null, { "rota": dados_rota, "fazendas": dados_fazendas, "fazendas_visitadas": fazendas_visitadas });
      })
      
    })
    
    // sql.query(`select F.*, V.status_visita from db_app_aiba.z_fazendas_cadastradas F INNER JOIN db_app_aiba.z_visitas_validacao V ON F.id_fzd = V.fzd_id WHERE id_fzd IN (${fazendas}) `, (err, res) => {
    //   result(null, { "rota": dados_rota, "fazendas": res });

    //   console.log(res)
    // });
  }


};

//buscar todas as rotas cadastradas
rota.relatoriorotas_pordata = (dados, result) => {
  sql.query("select * from z_rotas where DATE(data_encerra) between '2021-12-01' and '2022-01-31';", (err, res) => {
    if (err) {
      //se ocorrer um erro
      console.log("error: ", err);
      result(null, err);
      return;
    }
    //se a busca de todas as rotas suceder 
    //console.log("rotas: ", res);
    result(res, null);
  });
};

rota.relatoriorotas_periodo = (query, result) => {
  let resposta = []
  sql.query(`select sum(pessoas_abordadas) as pessoas_abordadas, sum(veiculos_4_rodas) as veiculos_4_rodas, sum(veiculos_2_rodas) as veiculos_2_rodas, sum(armas_apreendidas) as armas_apreendidas, sum(apresentacao_na_delegacia) as apresentacao_na_delegacia, sum(estabelecimento_abordado) as estabelecimento_abordado, sum(carga_recuperada) as carga_recuperada, sum(veiculo_recuperado) as veiculo_recuperado from z_rotas where DATE(data_encerra) between '${query.data_inicio}' and '${query.data_final}'; `, (err, res) => {
    if (err) {
      result(err, null)
      return;
    }

    if(res.length){
      res.map(res => {
        const objeto = {
          pessoas_abordadas: res.pessoas_abordadas,
          veiculos_4_rodas: res.veiculos_4_rodas,
          veiculos_2_rodas: res.veiculos_2_rodas,
          armas_apreendidas: res.armas_apreendidas,
          apresentacao_na_delegacia: res.apresentacao_na_delegacia,
          estabelecimento_abordado: res.estabelecimento_abordado,
          carga_recuperada: res.carga_recuperada,
          veiculo_recuperado: res.veiculo_recuperado
        }
        resposta.push(objeto)
      })
      result(null, resposta);
      return;

    }
    else{
      result({message: `Não há ocorrências para o periodo [  ${query.data_inicio} -- ${query.data_final} ] `}, null)
    }
  })
};

rota.relatoriorotas_comarea = (query, result) => {
  let resposta = []
  sql.query(`select sum(pessoas_abordadas) as pessoas_abordadas, sum(veiculos_4_rodas) as veiculos_4_rodas, sum(veiculos_2_rodas) as veiculos_2_rodas, sum(armas_apreendidas) as armas_apreendidas, sum(apresentacao_na_delegacia) as apresentacao_na_delegacia, sum(estabelecimento_abordado) as estabelecimento_abordado, sum(carga_recuperada) as carga_recuperada, sum(veiculo_recuperado) as veiculo_recuperado from z_rotas where id_area = ${query.id_area} and DATE(data_encerra) between '${query.data_inicio}' and '${query.data_final}'; `, (err, res) => {
    if (err) {
      result(err, null)
      return;
    }

    if(res.length){
      res.map(res => {
        const objeto = {
          pessoas_abordadas: res.pessoas_abordadas,
          veiculos_4_rodas: res.veiculos_4_rodas,
          veiculos_2_rodas: res.veiculos_2_rodas,
          armas_apreendidas: res.armas_apreendidas,
          apresentacao_na_delegacia: res.apresentacao_na_delegacia,
          estabelecimento_abordado: res.estabelecimento_abordado,
          carga_recuperada: res.carga_recuperada,
          veiculo_recuperado: res.veiculo_recuperado
        }
        resposta.push(objeto)
      })
      result(null, resposta);
      return;

    }
    else{
      result({message: `Não há ocorrências para o periodo [  ${query.data_inicio} -- ${query.data_final} ] `}, null)
    }
  })
};

rota.relatoriorotas_unidade = (query, result) => {
  let resposta = []
  sql.query(`select sum(pessoas_abordadas) as pessoas_abordadas, sum(veiculos_4_rodas) as veiculos_4_rodas, sum(veiculos_2_rodas) as veiculos_2_rodas, sum(armas_apreendidas) as armas_apreendidas, sum(apresentacao_na_delegacia) as apresentacao_na_delegacia, sum(estabelecimento_abordado) as estabelecimento_abordado, sum(carga_recuperada) as carga_recuperada, sum(veiculo_recuperado) as veiculo_recuperado from z_rotas where unidade = '${query.unidade}' and DATE(data_encerra) between '${query.data_inicio}' and '${query.data_final}'; `, (err, res) => {
    if (err) {
      result(err, null)
      return;
    }

    if(res.length){
      res.map(res => {
        const objeto = {
          pessoas_abordadas: res.pessoas_abordadas,
          veiculos_4_rodas: res.veiculos_4_rodas,
          veiculos_2_rodas: res.veiculos_2_rodas,
          armas_apreendidas: res.armas_apreendidas,
          apresentacao_na_delegacia: res.apresentacao_na_delegacia,
          estabelecimento_abordado: res.estabelecimento_abordado,
          carga_recuperada: res.carga_recuperada,
          veiculo_recuperado: res.veiculo_recuperado
        }
        resposta.push(objeto)
      })
      result(null, resposta);
      return;

    }
    else{
      result({message: `Não há ocorrências para o periodo [  ${query.data_inicio} -- ${query.data_final} ] `}, null)
    }
  })
};

rota.relatoriorotas_periodoareaunidade = (query, result) => {
  let resposta = []
  sql.query(`select sum(pessoas_abordadas) as pessoas_abordadas, sum(veiculos_4_rodas) as veiculos_4_rodas, sum(veiculos_2_rodas) as veiculos_2_rodas, sum(armas_apreendidas) as armas_apreendidas, sum(apresentacao_na_delegacia) as apresentacao_na_delegacia, sum(estabelecimento_abordado) as estabelecimento_abordado, sum(carga_recuperada) as carga_recuperada, sum(veiculo_recuperado) as veiculo_recuperado from z_rotas where id_area = ${query.id_area} and unidade = '${query.unidade}' and DATE(data_encerra) between '${query.data_inicio}' and '${query.data_final}'; `, (err, res) => {
    if (err) {
      result(err, null)
      return;
    }

    if(res.length){
      res.map(res => {
        const objeto = {
          pessoas_abordadas: res.pessoas_abordadas,
          veiculos_4_rodas: res.veiculos_4_rodas,
          veiculos_2_rodas: res.veiculos_2_rodas,
          armas_apreendidas: res.armas_apreendidas,
          apresentacao_na_delegacia: res.apresentacao_na_delegacia,
          estabelecimento_abordado: res.estabelecimento_abordado,
          carga_recuperada: res.carga_recuperada,
          veiculo_recuperado: res.veiculo_recuperado
        }
        resposta.push(objeto)
      })
      result(null, resposta);
      return;

    }
    else{
      result({message: `Não há ocorrências para o periodo [  ${query.data_inicio} -- ${query.data_final} ] `}, null)
    }
  })
};

module.exports = rota;
