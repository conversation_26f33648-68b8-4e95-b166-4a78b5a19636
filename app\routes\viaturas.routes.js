module.exports = app => {
  //
    const Viaturas = require("../controllers/viaturas.controller.js");
  ////
  
    //rota para salvar uma nova viatura 
    app.post("/salvar_viatura", Viaturas.salvarviatura)

    //rota para buscar uma viatura pelo id
    app.post("/buscar_viatura", Viaturas.buscarviatura)

    //rota para buscar viaturas por area de atuação
    app.post("/buscar_viatura_atuacao", Viaturas.buscarviatura_por_area)

    //rota para retornar todas as viaturas
    app.get("/todas_viaturas", Viaturas.todasviaturas)

    //atualizar os dados de uma viatura a partir do seu id
    app.put("/atualizar_viatura", Viaturas.atualizarviatura)  
    
    // // Delete a Customer with customerId
    // app.delete("/customers/:customerId", customers.delete);

    //seleciona todas as viaturas da unidade pelo ID correspondente ao PM 
    app.post("/todas_viatura_unidade", Viaturas.todasviaturaunidade)
        
    
  };
  