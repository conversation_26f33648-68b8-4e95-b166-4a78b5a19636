const panico_model = require("../models/panico.model.js");
var requestIp = require('request-ip');
const Panico = require("../models/panico.model.js");
const axios = require('axios')

exports.disparo_panico = (req, res) => {

  var clientIp = requestIp.getClientIp(req);
  if (!req.body) {
    res.status(400).send({
      message: "Content can not be empty!"
    });//

    return;
  }

  const panico = new panico_model({
    latitude: req.body.latitude,
    longitude: req.body.longitude,
    hora: req.body.hora,
    user_id: req.body.user_id,
    tipo: req.body.tipo,
    STATUS: "0",
    IP: clientIp,
    PROPRIETARIO: req.body.PROPRIETARIO,
    NOME_FAZENDA: req.body.NOME_FAZENDA,
    NOME_AREAFZD: req.body.NOME_AREAFZD,
    VIATURA: req.body.VIATURA


  });
  let token;

  if (!req.body.latitude) {
    res.status(404).send({ message: "latitude não pode ser nula" })
  }

  if (!req.body.longitude) {
    res.status(404).send({ message: "longitude não pode ser nula" })
  }

  if (!req.body.PROPRIETARIO)
    res.status(404).send({ message: "ID do proprietário não pode ser nulo" })

  if (!req.body.NOME_FAZENDA)
    res.status(404).send({ message: "ID da fazenda não pode ser nulo" })

  if (!req.body.NOME_AREAFZD)
    res.status(404).send({ message: "ID da área da fazenda não pode ser nulo" })



  panico_model.disparo(panico, async (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while creating the Customer."
      });
    else {
      await Panico.get_equipment_by_fzd_id(req.body.NOME_FAZENDA, async (err, data) => {
        if (err)
          console.log(err)
        else {
          await axios.post(`https://activecloud.cloud/api/v1/auth`, {
            email: data.user,
            password: data.password
          })
            .then(res => {
              console.log(res.data)
              token = res.data
            })
            .catch(err => {
              console.log(err)
            })

          await axios.get(`https://activecloud.cloud/api/v1/devices`, {
            headers: {
              "X-Access-Token": token.token
            }
          })
            .then(res => {
              devices = res.data
            })
            .catch(err => {
              console.log(err)
            })

          await axios.put(`https://activecloud.cloud/api/v1/devices/${data.equipment_id}`, {
            cmd: "arm"
          }, {
            headers: {
              "X-Access-Token": token.token
            }

          })
            .then(async res => {
              await sleep(2000)
              response_arm = res.data
              axios.put(`https://activecloud.cloud/api/v1/devices/${data.equipment_id}`, {
                cmd: "shoot"
              }, {
                headers: {
                  "X-Access-Token": token.token
                }

              })
                .then(res => {
                  console.log(res.data)
                  response_arm = res.data
                })
                .catch(err => {
                  console.log(err)
                })
            })
            .catch(err => {
              console.log(err)
            })


        }
      })
      res.send(data);
    }
  });

};

//mostra todos os panicos cadastrados
exports.todospanicos = (req, res) => {

  Panico.todos_panicos((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Algum erro ocorreu ao tentar recuperar panicos"
      });
    else res.send(data);
  });
};

//mostra todos os panicos cadastrados
exports.get_panico_id = (req, res) => {

  Panico.panicos_id(req.body.id, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Algum erro ocorreu ao tentar recuperar panicos"
      });
    else res.send(data);
  });

};

exports.detalhes_panico = (req, res) => {
  Panico.detalhes_panico(req.body.id, (err, data) => {
    if (err)
      res.status(400).send({
        message:
          err.message || "Algum erro ocorreu ao tentar recuperar os detalhes do panico"
      });
    else res.send(data);
  })
}

exports.get_panico_id_fazenda = (req, res) => {
  Panico.panico_id_fazenda(req.body.id, (err, data) => {
    if (err)
      res.status(400).send({
        message:
          err.message || "Algum erro ocorreu ao tentar recuperar os detalhes do panico"
      });
    else res.send(data);
  })
}

exports.panico_nevoa_testing = async (req, res) => {
  let token;
  let devices;
  let details;
  let response_arm;
  if (!req.body.NOME_FAZENDA) {
    return res.status(400).send({
      message: "Inclua o NOME_FAZENDA"
    })
  }
  await Panico.get_equipment_by_fzd_id(req.body.NOME_FAZENDA, async (err, data) => {
    if (err)
      console.log(err)
    else {
      await axios.post(`https://activecloud.cloud/api/v1/auth`, {
        email: data.user,
        password: data.password
      })
        .then(res => {
          console.log(res.data)
          token = res.data
        })
        .catch(err => {
          console.log(err)
        })

      await axios.get(`https://activecloud.cloud/api/v1/devices`, {
        headers: {
          "X-Access-Token": token.token
        }
      })
        .then(res => {
          devices = res.data
        })
        .catch(err => {
          console.log(err)
        })
      await axios.put(`https://activecloud.cloud/api/v1/devices/${data.equipment_id}`, {
        cmd: "arm"
      }, {
        headers: {
          "X-Access-Token": token.token
        }

      })
        .then(res => {
          response_arm = res.data
        })
        .catch(err => {
          console.log(err)
        })

      await axios.put(`https://activecloud.cloud/api/v1/devices/${data.equipment_id}`, {
        cmd: "shoot"
      }, {
        headers: {
          "X-Access-Token": token.token
        }

      })
        .then(res => {
          response_arm = res.data
        })
        .catch(err => {
          console.log(err)
        })

      await axios.get(`https://activecloud.cloud/api/v1/devices/${data.equipment_id}`, {
        headers: {
          "X-Access-Token": token.token
        }
      })
        .then(res => {
          details = res.data
        })
        .catch(err => {
          console.log(err)
        })
    }
    res.send(details)
  })
}

function sleep(ms) {
  return new Promise((resolve) => {
    setTimeout(resolve, ms);
  });
}