# 🔧 CORS Troubleshooting Guide

## 🚨 Current Status
O CORS foi configurado temporariamente de forma **PERMISSIVA** para debug. Isso permite todas as origens e registra logs detalhados.

## 📋 Passos para Debug

### 1. **Verificar Logs do Container**
```bash
# No Coolify, vá em Logs e procure por:
CORS request from origin: https://seu-frontend.com
Socket.IO request from origin: https://seu-frontend.com
```

### 2. **Testar Endpoints Básicos**
```bash
# Teste o health check
curl -H "Origin: https://seu-frontend.com" https://aibabackend.agilemakers.com.br/health

# Teste com CORS headers
curl -H "Origin: https://seu-frontend.com" \
     -H "Access-Control-Request-Method: GET" \
     -H "Access-Control-Request-Headers: Content-Type" \
     -X OPTIONS \
     https://aibabackend.agilemakers.com.br/health
```

### 3. **Verificar Headers de Resposta**
Procure por estes headers na resposta:
```
Access-Control-Allow-Origin: *
Access-Control-Allow-Credentials: true
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, Accept, Origin
```

## 🔒 Configuração Segura (Para Depois do Debug)

Depois de identificar o problema, substitua a configuração permissiva por:

```javascript
const corsOptions = {
  origin: [
    'https://aibabackend.agilemakers.com.br',
    'https://aiba.agilemakers.com.br',
    'https://seu-frontend.com.br'  // Adicione seu frontend aqui
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};
```

## 🐛 Problemas Comuns

### 1. **Frontend não especificado**
- Adicione o domínio do seu frontend na lista de origens permitidas

### 2. **Protocolo HTTP vs HTTPS**
- Certifique-se de usar HTTPS em produção
- Verifique se o frontend está fazendo requests para HTTPS

### 3. **Subdomínios**
- `www.exemplo.com` é diferente de `exemplo.com`
- Adicione ambos se necessário

### 4. **Portas**
- `https://exemplo.com` é diferente de `https://exemplo.com:3000`

## 📝 Variáveis de Ambiente

Você pode configurar origens adicionais via variável de ambiente:
```bash
ALLOWED_ORIGINS=https://meuapp.com,https://www.meuapp.com,https://admin.meuapp.com
```

## 🔄 Próximos Passos

1. **Deploy com CORS permissivo** ✅
2. **Verificar logs** - Identifique qual origem está sendo bloqueada
3. **Adicionar origem específica** - Na lista de origens permitidas
4. **Testar** - Verificar se funciona
5. **Remover configuração permissiva** - Voltar para configuração restritiva
