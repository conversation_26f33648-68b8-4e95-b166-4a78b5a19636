module.exports = app => {
  
    const areas = require("../controllers/areas_atuacao.controller.js");
  
  
    //rota para salvar uma nova area 
    app.post("/salvar_area", areas.salvararea)

    //rota para buscar uma area pelo id
    app.post("/buscar_area", areas.buscararea)//

    //rota para retornar todas as areas
    app.get("/todas_areas", areas.todasareas)

    //atualizar os dados de uma area a partir do seu id//
    app.put("/atualizar_area", areas.atualizararea)  
    


    // //atualizar os dados de uma area a partir do seu id
    // app.post("/atualizar_area", areas.atualizararea)

    

    
    
    
    // ROTAS PARA TESTAR  
    // // Retrieve a single Customer with customerId
    // app.get("/customers/:customerId", customers.findOne);

    // // Update a Customer with customerId
    // app.put("/customers/:customerId", customers.update);

    // // Delete a Customer with customerId
    // app.delete("/customers/:customerId", customers.delete);

    // // Create a new Customer
    // app.delete("/customers", customers.deleteAll);
    
  };
  