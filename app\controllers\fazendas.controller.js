const fazenda = require("../models/fazendas.model.js");
const fazenda_model = require("../models/fazendas.model.js");



// Cria e salva uma nova fazenda
exports.salvarfazenda = (req, res) => {


  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"

    });

    return;
  }

  // Cria uma fazenda

  const fazenda = new fazenda_model({
    nome_da_fazenda: req.body.nome_da_fazenda,
    proprietario: req.body.proprietario,
    id_area: req.body.id_area,
    area_de_operacao: req.body.area_de_operacao,
    municipio: req.body.municipio,
    codigo_da_fazenda: req.body.codigo_da_fazenda,
    latitude: req.body.latitude,
    longitude: req.body.longitude,
    data_do_cadastro: req.body.data_do_cadastro,
    hora_do_cadastro: req.body.hora_do_cadastro,
    ativo_inativo: req.body.ativo_inativo,
    pernoite: req.body.pernoite,
    combustivel: req.body.combustivel
  });

  // Salva fazenda no banco de dados
  fazenda_model.salvar_fazenda(fazenda, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Ocorreu algum erro ao criar fazenda."
      });
    else res.send(data);
  });

};

// Busca uma unica fazenda pelo seu id
exports.buscarfazenda = (req, res) => {

  fazenda.buscar_fazenda(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrada fazenda com id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar fazenda com id " + req.body
        });
      }
    } else res.send(data);
  });
};


// Busca Extras pelo id da fazenda
exports.buscarextras = (req, res) => {

  fazenda.buscar_extras(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrada fazenda com id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar fazenda com id " + req.body
        });
      }
    } else res.send(data);
  });
};

//atualiza os dados de um extras identificada pelo id na chamada
exports.atualizarextras = (req, res) => {
  //validar solicitação
  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"
    });
  }

  console.log(req.body);
  //verificando se há erros na busca ou na atualização para se não, retornar os dados atualizados
  fazenda.atualizar_extras(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Não encontrada extras da fazenda com id ${req.params.atualizarfazenda}.`
        });
      } else {
        res.status(500).send({
          message: "erro ao atualizar extras da fazenda com id " + req.params.atualizarfazenda
        });
      }
    } else res.send(data);
  }
  );
};


// Busca fazendas do proprietario pelo seu id
exports.buscarfazendaproprietario = (req, res) => {

  fazenda.buscar_fazenda_proprietario(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrada fazenda com id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar fazenda com id " + req.body
        });
      }
    } else res.send(data);
  });
};


// Busca as fazendas pelo id da area de atuação
exports.buscarfazendas = (req, res) => {

  fazenda.buscar_fazendas(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrada fazenda com id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar fazenda com id " + req.body
        });
      }
    } else res.send(data);
  });
};


//mostra todas as fazendas cadastradas
exports.todasfazendas = (req, res) => {
  fazenda.todas_fazendas((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Algum erro ocorreu ao tentar recuperar fazendas"
      });
    else res.send(data);
  });
};

exports.visitas_fazendas = (req, res) => {
  fazenda.visitas_fazendas((err, data) => {
    if(err)
    res.status(500).send({
      message:
        err.message || "Algum erro ocorreu ao tentar as visitas de todas as fazendas fazendas"
    });
    else res.send(data)
  })
}

//atualiza os dados de uma fazenda identificada pelo id na chamada
exports.atualizarfazenda = (req, res) => {
  //validar solicitação
  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"
    });
  }

  console.log(req.body);
  //verificando se há erros na busca ou na atualização para se não, retornar os dados atualizados
  fazenda.atualizar_fazenda(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Não encontrada fazenda com id ${req.params.atualizarfazenda}.`
        });
      } else {
        res.status(500).send({
          message: "erro ao atualizar fazenda com id " + req.params.atualizarfazenda
        });
      }
    } else res.send(data);
  }
  );
};

//delete uma fazenda com o id especificado na chamada
exports.deletarfazenda = (req, res) => {

  fazenda.deletar_fazenda(req.body.id, (err, data) => {

    if (err) {

      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found Customer with id ${req.params.customerId}.`
        });
      } else {
        res.status(500).send({
          message: "Could not delete Customer with id " + req.params.customerId
        });
      }
      
    } else res.send({ message: `Customer was deleted successfully!` });
    
  });

};





