module.exports = app => {
  
    const proprietarios = require("../controllers/proprietarios.controller.js");
  
    //rota para salvar uma nova proprietario 
    app.post("/salvar_proprietario", proprietarios.salvarproprietario)

    //rota para buscar uma proprietario pelo id
    app.post("/buscar_proprietario", proprietarios.buscarproprietario)

    //rota para retornar todas as proprietarios
    app.get("/selecionar_todos_proprietarios", proprietarios.todosproprietarios)

    //atualizar os dados de uma fazenda a partir do seu id
    app.put("/atualizar_proprietario", proprietarios.atualizarproprietario)
    
    // // Delete a Customer with customerId
    // app.delete("/customers/:customerId", customers.delete);

    //rota para enviar email para o pm
    // app.post("/send_mail", Email.prepare_email)

    
  };
  