const sql = require("./db.js");


const iniciacao = function (iniciacao) {
  this.id_viatura = iniciacao.id_viatura;
  // this.viatura = iniciacao.viatura;
  this.id_agente_responsavel = iniciacao.id_agente_responsavel;
  //this.agente_responsavel = iniciacao.agente_responsavel;
  //this.data_inicio = iniciacao.data_inicio;
  this.hora_inicio = iniciacao.hora_inicio;
  //this.data_final = iniciacao.data_final;
  //this.hora_final = iniciacao.hora_final;
  this.quilometragem_atual = iniciacao.quilometragem_atual;
  // this.agentes_na_viatura = iniciacao.agentes_na_viatura;
  this.id_rota = iniciacao.id_rota;

};

//salvar iniciacao
iniciacao.salvar_iniciacao = (nova_iniciacao, result) => {
  sql.query("INSERT INTO z_visitas_iniciacao SET ?", nova_iniciacao, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    console.log("iniciacao criada: ", { id: res.insertId, ...nova_iniciacao });
    result(null, { id: res.insertId, ...nova_iniciacao });
  });
};

//buscar uma unica iniciacao pelo id
iniciacao.buscar_iniciacao = (json, result) => {
  sql.query(`SELECT * FROM z_visitas_iniciacao WHERE id = ${json.id_fzd}`, (err, res) => {
    //se ocorrer um erro
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca da iniciacao suceder
    if (res.length) {
      console.log("iniciacao encontrada: ", res[0]);
      result(null, res[0]);
      return;
    }

    //quando não encontrada a iniciacao pelo id
    result({ kind: "not_found" }, null);
  });
};

//buscar as iniciacoes relacionadas ao proprietario pelo id proprietario
iniciacao.buscar_iniciacao_proprietario = (json, result) => {
  sql.query(`SELECT * FROM db_app_aiba.z_visitas_iniciacao WHERE id = '${json.id}'`, (err, res) => {
    //se ocorrer um erro
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca da iniciacao suceder
    if (res.length) {
      console.log("iniciacao(s) encontrada(s): ", res);
      result(null, res);
      return;
    }

    //quando não encontrada a iniciacao pelo id
    result({ kind: "not_found" }, null);
  });
};

//buscar uma unica iniciacao pelo id
iniciacao.buscar_iniciacoes = (json, result) => {
  sql.query(`SELECT * FROM db_app_aiba.z_visitas_iniciacao where z_visitas_iniciacao.id = ${json.id}`, (err, res) => {
    //se ocorrer um erro
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca da iniciacao suceder
    if (res.length) {
      console.log("iniciacao encontrada: ", res);
      result(null, res);
      return;
    }

    //quando não encontrada a iniciacao pelo id
    result({ kind: "not_found" }, null);
  });
};

//buscar todas as iniciacoes cadastradas
iniciacao.todas_iniciacoes = result => {
  sql.query("SELECT VI.*, R.responsavel, R.data_inicio, R.`status`, R.componentes_policiais, V.km_inicial FROM db_app_aiba.z_rotas R JOIN db_app_aiba.z_visitas_iniciacao VI ON R.id = VI.id_rota JOIN db_app_aiba.z_pmba_cad_viat V ON V.id_viat = R.id_viat ;", (err, res) => {
    if (err) {
      //se ocorrer um erro
      console.log("error: ", err);
      result(null, err);
      return;
    }
    //se a busca de todas as iniciacoes suceder 
    console.log("iniciacoes: ", res);
    result(null, res);
  });
};

//atualiza os dados de uma iniciacao pelo seu id
iniciacao.atualizar_iniciacao = (iniciacao, result) => {
  console.log(iniciacao)
  sql.query(
    "UPDATE z_visitas_iniciacao SET id_viatura = ? hora_inicio = ?, quilometragem_atual = ? WHERE id = ?",
    [iniciacao.id_viatura, iniciacao.hora_inicio, iniciacao.quilometragem_atual, iniciacao.id],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }

      if (res.affectedRows == 0) {
        //não encontrada iniciacao com o id
        result({ kind: "iniciacao não encontrada" }, null);
        return;
      }

      result(null, "OK" );
    }
  );
};

//delete uma iniciacao com o id especificado na chamada
iniciacao.deletar_iniciacao = (id, result) => {

  sql.query("DELETE FROM `db_app_aiba`.`z_iniciacoes_cadastradas` WHERE z_iniciacoes_cadastradas.id = ?;", id, (err, res) => {
    if (err) {
      result(err, null);
      return;
    }

    if (res.affectedRows == 0) {
      // not found iniciacao with the id
      result({ kind: "not_found" }, null);
      return;
    }

    console.log("deleted iniciacao with id: ", id);
    result(null, res);
  });
  
};


module.exports = iniciacao;


