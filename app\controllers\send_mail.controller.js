const mail_model = require("../models/send_mail.model.js");

// Cria e salva uma nova rota
exports.prepare_email = (req, res) => {

  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"

    });
    return;
  }

  const mail = new mail_model({
    email: req.body.email,
    texto: req.body.texto,
    opcao: req.body.opcao
  });
  
  mail_model.fire_email(mail, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Ocorreu algum erro ao criar rota."
      });
    else res.send(data);
  });

};

