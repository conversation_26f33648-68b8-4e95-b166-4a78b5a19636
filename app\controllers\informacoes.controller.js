const informacao = require("../models/informacoes.model.js");
const informacao_model = require("../models/informacoes.model.js");



// Cria e salva uma nova informacao
exports.salvarinformacao = (req, res) => {


  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"

    });

    return;
  }

  // Cria uma informacao

  const informacao = new informacao_model({
    id_prop: req.body.id_prop,
    motivo: req.body.motivo,
    descricao: req.body.descricao,
    onde_registrou: req.body.onde_registrou,
    dia: req.body.dia,
    hora: req.body.hora
  });

  // Salva informacao no banco de dados
  informacao_model.salvar_informacao(informacao, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Ocorreu algum erro ao criar informacao."
      });
    else res.send(data);
  });

};

// Busca uma unica informacao pelo seu id
exports.buscarinformacao = (req, res) => {

  informacao.buscar_informacao(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrada informacao com id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar informacao com id " + req.body
        });
      }
    } else res.send(data);
  });
};

// Busca informacoes do proprietario pelo seu id
exports.buscarinformacaoproprietario = (req, res) => {

  informacao.buscar_informacao_proprietario(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrada informacao com id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar informacao com id " + req.body
        });
      }
    } else res.send(data);
  });
};


// Busca as informacoes pelo id da area de atuação
exports.buscarinformacoes = (req, res) => {

  informacao.buscar_informacoes(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrada informacao com id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar informacao com id " + req.body
        });
      }
    } else res.send(data);
  });
};


//mostra todas as informacoes cadastradas
exports.todasinformacoes = (req, res) => {
  informacao.todas_informacoes((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Algum erro ocorreu ao tentar recuperar informacoes"
      });
    else res.send(data);
  });
};

//atualiza os dados de uma informacao identificada pelo id na chamada
exports.atualizarinformacao = (req, res) => {
  //validar solicitação
  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"
    });
  }

  console.log(req.body);
  //verificando se há erros na busca ou na atualização para se não, retornar os dados atualizados
  informacao.atualizar_informacao(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Não encontrada informacao com id ${req.params.atualizarinformacao}.`
        });
      } else {
        res.status(500).send({
          message: "erro ao atualizar informacao com id " + req.params.atualizarinformacao
        });
      }
    } else res.send(data);
  }
  );
};

//delete uma informacao com o id especificado na chamada
exports.deletarinformacao = (req, res) => {

  informacao.deletar_informacao(req.body.id, (err, data) => {

    if (err) {

      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found Customer with id ${req.params.customerId}.`
        });
      } else {
        res.status(500).send({
          message: "Could not delete Customer with id " + req.params.customerId
        });
      }

    } else res.send({ message: `Customer was deleted successfully!` });

  });

};


//mostra todos os motivos possíveis ao informar na criação de uma informação
exports.motivosinformacao = (req, res) => {
  informacao.motivosinformacao((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Algum erro ocorreu ao tentar recuperar informacoes"
      });
    else res.send(data);
  });
};
 
//Abrir uma nova informação enviando os dados de uma informacao a partir dos ids respectivos (Proprietário, Fazendeiro e Motivo) bem como uma descrição enviada pelo informante
exports.abririnformacao = (req, res) => {
  var regra = /^[0-9]+$/;

  //validar solicitação
  if (!req.body) {
    res.status(404).send({
      message: "Conteudo não pode estar vazio!"
    });
  }

  //Verifica id_prop
  if(!req.body.id_prop){
    res.status(404).send({
      message: "id_prop não pode ser nulo"
    })
    return;
  }

  valor = req.body.id_prop;
  if (!(valor.match(regra))) {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
       res.status(404).send({
        message: "id_prop deve ser um valor numérico não negativo"
      })
      return;
  }

  //Verifica id_fzd
  if(!req.body.id_fzd){
    res.status(404).send({
      message: "id_fzd não pode ser nulo"
    })
    return;
  }

  delete valor;
  valor = req.body.id_fzd;
  if (!(valor.match(regra))) {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
       res.status(404).send({
        message: "id_fzd deve ser um valor numérico não negativo"
      })
      return;
  }

  //Verifica id_motivo
  if(!req.body.id_motivo){
    res.status(404).send({
      message: "id_motivo não pode ser nulo"
    })
    return;
  }

  delete valor;
  valor = req.body.id_motivo;
  if (!(valor.match(regra))) {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
       res.status(404).send({
        message: "id_motivo deve ser um valor numérico não negativo"
      })
      return;
  }

  //Verifica registrado_fora_delegacia
  if(!req.body.registrado_fora_delegacia){
    res.status(404).send({
      message: "registrado_fora_delegacia não pode ser nulo"
    })
    return;
  }

  valorRegistradoDelegacia = parseInt(req.body.registrado_fora_delegacia);
  console.log("valorDelegacia*********************");
  console.log(valorRegistradoDelegacia);
  if (valorRegistradoDelegacia>1) {
    res.status(404).send({
      message: "registrado_fora_delegacia com valor inválido"
    })
    return;
  }

  //Verifica descricao
  if(!req.body.descricao){
    res.status(404).send({
      message: "descricao não pode ser nulo"
    })
    return;
  }

  //Chamada ao model para fazer o INSERT no Database
  informacao.abririnformacao(req.body, (err, data) => {
      if (err) {
        res.status(500).send({
          message: "erro ao abrir informacao com os dados " + req.body,
        });
      } else res.send(data);
  }
  )};

//mostra as informacoes ordenadas pelas mais recentes
exports.informacoes = (req, res) => {
  informacao.informacoes((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Algum erro ocorreu ao tentar recuperar informacoes"
      });
    else res.send(data);
  });
};
