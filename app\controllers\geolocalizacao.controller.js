const geolocalizacao = require("../models/salvar_geo.model");
var requestIp = require('request-ip');
let date_ob = new Date();
let date = ("0" + date_ob.getDate()).slice(-2);
let month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
let year = date_ob.getFullYear();
let hours = date_ob.getHours();
let minutes = date_ob.getMinutes();
let seconds = date_ob.getSeconds();
var date_now = year + "-" + month + "-" + date + " " + hours + ":" + minutes + ":" + seconds

exports.salvar_geo = (req, res) => {
  console.log("Json >>>>>",req.body)
  var clientIp = requestIp.getClientIp(req);
  console.log("IP >>>>",clientIp)

  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"
    });

    return;
  }

  // Cria uma rota
  const Geolocalizacao = new geolocalizacao({
    ip: clientIp,
    latitude: req.body.latitude,
    longitude: req.body.longitude,
    id_usuario: req.body.userId,
    data_hora: date_now,
  });

  // Salva rota no banco de dados
  geolocalizacao.salvar_geolocalizacao(Geolocalizacao, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Ocorreu algum erro ao criar rota."
      });
    else res.send(data);
  });

};


