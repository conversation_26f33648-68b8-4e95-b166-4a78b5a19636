module.exports = app => {

  const Panico_controller = require("../controllers/panico.controller.js");
  const Panico_acaminho_controller = require("../controllers/panico_acaminho.controller.js");
  const Panico_finalizado_controller = require("../controllers/panico_finalizado.controller.js");
  const Panico_cancelado_controller = require("../controllers/panico_cancelado.controller.js");
  const Panico_ocorrencias_armada_controller = require("../controllers/panico_ocorrencias_armadas.controller.js");
  const Panico_ocorrencias_andamento_controller = require("../controllers/panico_ocorrencias_andamento.controller.js");
  const Panico_ocorrencias_encerradas_controller = require("../controllers/panico_ocorrencias_encerradas.controller.js");
  const Panico_last = require("../controllers/panico_last.controller.js");
  const Panico = require("../controllers/panico.controller.js");

  //rota para disparar panico //////
  app.post("/panico", Panico_controller.disparo_panico)

  //rota para disparar panico a caminho
  app.post("/panico_acaminho", Panico_acaminho_controller.disparo_panico_acaminho)

  //rota para disparar panico finalizado
  app.post("/panico_finalizado", Panico_finalizado_controller.disparo_panico_finalizado)

  //rota para disparar panico cancelado
  app.post("/panico_cancelada", Panico_cancelado_controller.disparo_panico_cancelado)

  //rota ocorrencias armadas
  app.get("/panico_ocorrencias_armada", Panico_ocorrencias_armada_controller.findAll)

  //rota ocorrencias andamento
  app.get("/panico_ocorrencias_andamento", Panico_ocorrencias_andamento_controller.findAll)

  //rota ocorrencias encerradas
  app.get("/panico_ocorrencias_encerradas", Panico_ocorrencias_encerradas_controller.findAll)

  //rota panico last
  app.get("/panico_last", Panico_last.findAll)

  //rota para retornar todos os panicos, sem restrições
  app.get("/todos_panicos", Panico.todospanicos)

   //rota para retornar panico pelo id
   app.post("/panico_id_viatura", Panico.get_panico_id)

   app.post("/detalhes_panico", Panico.detalhes_panico)

   app.post("/panico_id_fazenda", Panico.get_panico_id_fazenda);

  app.post("/panico_nevoa_testing", Panico.panico_nevoa_testing);

};
