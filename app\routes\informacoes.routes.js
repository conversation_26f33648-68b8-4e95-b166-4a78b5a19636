module.exports = app => {
  
    const informacoes = require("../controllers/informacoes.controller.js");
  
    //rota para salvar uma nova informacao 
    app.post("/salvar_informacao", informacoes.salvarinformacao)

    //rota para buscar uma informacao pelo id
    app.post("/buscar_informacao", informacoes.buscarinformacao)

    //rota para buscar informacoes de um proprietario pelo id
    app.post("/buscar_informacao_proprietario", informacoes.buscarinformacaoproprietario)

    //rota para buscar informacoes pelo id da area de atuação
    app.post("/buscar_informacoes_atuacao", informacoes.buscarinformacoes)

    //rota para retornar todas as informacoes
    app.get("/todas_informacoes", informacoes.todasinformacoes)

    //atualizar os dados de uma informacao a partir do seu id
    app.put("/atualizar_informacao", informacoes.atualizarinformacao);

    // Delete a Customer with customerId
    app.delete("/deletar_informacao", informacoes.deletarinformacao)

    //rota para retornar todos os motivos possíveis ao informar na criação de uma informação
    app.get("/motivos_informacao", informacoes.motivosinformacao)

    //abrir uma nova informação enviando os dados de uma informacao a partir dos ids respectivos (Proprietário, Fazendeiro e Motivo) bem como uma descrição enviada pelo informante
    app.post("/abrir_informacao", informacoes.abririnformacao);

    //listar todas informações a partir das mais recentes
    app.get("/informacoes", informacoes.informacoes);

  };