<!DOCTYPE html>
<html>

<head>
    <title>Socket.IO</title>
    <meta http-equiv="content-type" content="text/html; charset=utf-8" />
    <link href="/public/favicon.ico" rel="shortcut icon" type="image/x-icon" />
    <script type="text/javascript" src="/socket.io/socket.io.js"></script>
    <script type="text/javascript" src="client/player.js"></script>
    <script type="text/javascript">
        var socket = io.connect();
        // Get server time
        socket.on('message', function (time) {
            document.getElementById('time').innerHTML = time;
        });
        // Get course data by second from server
        socket.on('playCourse', function (data) {
            drawScreenshot(data.ssdata, $('#workingss'), $('#playerss'));
            drawWhiteboard(data.wbdata, $('#workingwb'), $('#playerwb'));
        });
    </script>
    <link href="/public/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="/public/css/Site.css" rel="stylesheet" type="text/css" />
    <script src="/public/scripts/jquery-1.10.2.min.js"></script>
    <script src="/public/scripts/bootstrap.min.js"></script>
    <!--jquery slider bar-->
    <!--<link rel="stylesheet" href="http://code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css">-->
    <!--<script src="http://code.jquery.com/ui/1.11.4/jquery-ui.js"></script>-->
    <link rel="stylesheet" href="/public/css/jquery-ui.css">
    <script src="/public/scripts/jquery-ui.js"></script>
</head>

<body>
    <P>AIBA API SOCKET & EXPRESS</body>p>
</body>

</html>