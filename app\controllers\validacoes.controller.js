const validacao = require("../models/validacoes.model.js");
const validacao_model = require("../models/validacoes.model.js");

//mostra horario
const geolocalizacao = require("../models/salvar_geo.model");
var requestIp = require('request-ip');
let date_ob = new Date();
let date = ("0" + date_ob.getDate()).slice(-2);
let month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
let year = date_ob.getFullYear();
let hours = date_ob.getHours();
let minutes = date_ob.getMinutes();
let seconds = date_ob.getSeconds();
var date_now = year + "-" + month + "-" + date;
var hour_now =  hours + ":" + minutes + ":" + seconds;



// Cria e salva uma nova validacao
exports.salvarvalidacao = (req, res) => {


  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"

    });

    return;
  }

  // Cria uma validacao
  const validacao = new validacao_model({
    latitude: req.body.latitude,
    longitude: req.body.longitude,
    status_visita: req.body.status_visita,
    data_validacao: date_now,
    hora_validacao: hour_now,
    id_viatura: req.body.id_viatura,    
    fzd_id: req.body.fzd_id,
    id_rota: req.body.id_rota,
    viatura: null,
    pernoite: req.body.pernoite,
    combustivel: req.body.combustivel
    
  });

  

  
  // Salva validacao no banco de dados
  validacao_model.salvar_validacao(validacao, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Ocorreu algum erro ao criar validacao."
      });
    else res.send("visita realizada com sucesso!");
  });

};

// Busca uma unica validacao pelo seu id
exports.buscarvalidacao = (req, res) => {

  validacao.buscar_validacao(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrada validacao com id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar validacao com id " + req.body
        });
      }
    } else res.send(data);
  });
};

// Busca validacoes do proprietario pelo seu id
exports.buscarvalidacaoproprietario = (req, res) => {

  validacao.buscar_validacao_proprietario(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrada validacao com id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar validacao com id " + req.body
        });
      }
    } else res.send(data);
  });
};


// Busca as validacoes pelo id da area de atuação
exports.buscarvalidacoes = (req, res) => {

  validacao.buscar_validacoes(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrada validacao com id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar validacao com id " + req.body
        });
      }
    } else res.send(data);
  });
};


//mostra todas as validacoes cadastradas
exports.todasvalidacoes = (req, res) => {
  validacao.todas_validacoes((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Algum erro ocorreu ao tentar recuperar validacoes"
      });
    else res.send(data);
  });
};

//atualiza os dados de uma validacao identificada pelo id na chamada
exports.atualizarvalidacao = (req, res) => {
  //validar solicitação
  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"
    });
  }

  console.log(req.body);
  //verificando se há erros na busca ou na atualização para se não, retornar os dados atualizados
  validacao.atualizar_validacao(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Não encontrada validacao com id ${req.params.atualizarvalidacao}.`
        });
      } else {
        res.status(500).send({
          message: "erro ao atualizar validacao com id " + req.params.atualizarvalidacao
        });
      }
    } else res.send(data);
  }
  );
};

//delete uma validacao com o id especificado na chamada
exports.deletarvalidacao = (req, res) => {

  validacao.deletar_validacao(req.body.id, (err, data) => {

    if (err) {

      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found Customer with id ${req.params.customerId}.`
        });
      } else {
        res.status(500).send({
          message: "Could not delete Customer with id " + req.params.customerId
        });
      }
      
    } else res.send({ message: `Customer was deleted successfully!` });
    
  });

};





