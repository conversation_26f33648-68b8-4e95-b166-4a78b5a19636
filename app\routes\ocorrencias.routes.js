const ocorrencias = require("../controllers/ocorrencias.controller.js");
module.exports = app => {
  
  const ocorrencias = require("../controllers/ocorrencias.controller.js");

  // rota para buscar motivos de ocorrencias
  app.get("/motivos_ocorrencia", ocorrencias.get_motivos_ocorrencia);

  app.post("/registrar_ocorrencia", ocorrencias.insert_registrar_ocorrencia);

  app.get("/ocorrencias", ocorrencias.get_ocorrencias);

  app.post("/enviar_viatura", ocorrencias.enviar_viatura);

  app.post("/concluir_ocorrencia", ocorrencias.concluir_ocorrencia);

  app.get("/detalhe_ocorrencia", ocorrencias.detalhe_ocorrencia);

  app.get("/ocorrencias_viatura", ocorrencias.ocorrencias_viatura);

  app.post("/cancelar_ocorrencia", ocorrencias.cancelar_ocorrencia);

  app.get("/ocorrencias_fazenda", ocorrencias.ocorrencias_fazenda);

}; 