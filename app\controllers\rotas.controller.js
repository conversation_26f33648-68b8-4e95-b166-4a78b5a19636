const rota = require("../models/rotas.model.js");
const rota_model = require("../models/rotas.model.js");
const moment = require("moment");

//mostra horario
const geolocalizacao = require("../models/salvar_geo.model");
var requestIp = require('request-ip');
let date_ob = new Date();
let date = ("0" + date_ob.getDate()).slice(-2);
let month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
let year = date_ob.getFullYear();
let hours = date_ob.getHours();
let minutes = date_ob.getMinutes();
let seconds = date_ob.getSeconds();
var date_now = year + "-" + month + "-" + date;
var hour_now =  hours + ":" + minutes + ":" + seconds;


exports.validaParametro_INT_Nao_Negativo = (valor) => {
  var regra = /^[0-9]+$/;
  if (valor.match(regra)) {
    // Valor inteiro não negativo, portanto é válido
    return true;
  } else {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
    return false;
  }
};

// Cria e salva uma nova rota
exports.salvarrota = (req, res) => {


  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"

    });

    return;
  }

// "data_criacao": "7/9/2021",
// "hora_criacao": "10:39:17",
// "id_area": "3",
// "lista_fazendas": "3",
// "status": "Aguardando",

  // Cria uma rota
  const rota = new rota_model({
    id: null,
    data_criacao: date_now,
    hora_criacao: hour_now,
    id_area: req.body.id_area,
    id_viat: null,
    unidade: null,
    data_inicio: req.body.data_inicio,
    hora_inicio: null,
    lista_fazendas: req.body.lista_fazendas,
    kilometragem_inicial: null,
    kilometragem_estimada: null,
    componentes_policiais: null,
    responsavel: null,
    status: req.body.status,
    data_encerra: null,
    hora_encerra: null

  });

  // Salva rota no banco de dados
  rota_model.salvar_rota(rota, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Ocorreu algum erro ao criar rota."
      });
    else res.send(data);
  });

};

// Busca uma unica rota pelo seu id
exports.buscarrota = (req, res) => {

  rota.buscar_rota(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrada rota com id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar rota com id " + req.body
        });
      }
    } else res.send(data);
  });
};

// Busca uma unica rota pelo seu id
exports.detalheresponsavel = (req, res) => {

  rota.detalhe_responsavel(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrado por essa ID viatura: ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar por id:" + req.body
        });
      }
    } else res.send(data);
  });
};

// Busca todas as rotas criadas pelo id da fazenda
exports.detalhevisita = (req, res) => {

  if(!req.body.id){
    res.status(404).send({
      message: "Favor enviar o ID"
    })
  }

  rota.detalhe_visita(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrado por essa ID fazenda: ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar por id:" + req.body
        });
      }
    } else res.send(data);
  });
};

//mostra todas as rotas cadastradas
exports.todasrotas = (req, res) => {
  rota.todas_rotas(req.query.areaId, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Algum erro ocorreu ao tentar recuperar rotas"
      });
    else res.send(data);
  });
};

//atualiza os dados de uma rota identificada pelo id na chamada
exports.atualizarrota = (req, res) => {
  //validar solicitação
  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"
    });
  }

  console.log(req.body);
  //verificando se há erros na busca ou na atualização para se não, retornar os dados atualizados
  rota.atualizar_rota(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Não encontrada rota com id ${req.params.atualizarrota}.`
        });
      } else {
        res.status(500).send({
          message: "erro ao atualizar rota com id " + req.params.atualizarrota
        });
      }
    } else res.send(data);
  }
  );
};


//atualiza os dados de uma rota 2
exports.atualizarrota_2 = (req, res) => {
  //validar solicitação
  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"
    });
  }

  rota.atualizar_rota_2(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Não encontrada rota com id ${req.params.atualizarrota}.`
        });
      } else {
        res.status(500).send({
          message: "erro ao atualizar rota com id " + req.params.atualizarrota
        });
      }
    } else res.send(data);
  }
  );
};

//salva a etapa 2 da rota a partir do seu id
exports.salvarrota2 = (req, res) => {
  //validar solicitação
  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"
    });
  }
  console.log(req.body);
  //verificando se há erros na busca ou na atualização para se não, retornar os dados atualizados
  rota.salvar_rota2(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Não encontrada rota com id ${req.params.atualizarrota}.`
        });
      } else {
        res.status(500).send({
          message: "erro ao atualizar rota com id " + req.params.atualizarrota
        });
      }
    } else res.send(data);
  }
  );
};

//salva a etapa 3 da rota a partir do seu id
exports.salvarrota3 = (req, res) => {
  //validar solicitação
  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"
    });
  }
  console.log(req.body);
  //verificando se há erros na busca ou na atualização para se não, retornar os dados atualizados
  rota.salvar_rota3(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Não encontrada rota com id ${req.params.atualizarrota}.`
        });
      } else {
        res.status(500).send({
          message: "erro ao atualizar rota com id " + req.params.atualizarrota
        });
      }
    } else res.send(data);
  }
  );
};

//delete uma rota com o id especificado na chamada
exports.deletarrota = (req, res) => {

  rota.deletar_rota(req.body.id, (err, data) => {

    if (err) {

      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found Customer with id ${req.params.customerId}.`
        });
      } else {
        res.status(500).send({
          message: "Could not delete Customer with id " + req.params.customerId
        });
      }

    } else res.send({ message: `Customer was deleted successfully!` });

  });
};

//rota para buscar uma fazenda pelo id da rota**
exports.buscarfazendarota = (req, res) => {

  rota.buscar_fazenda_rota(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrada rota com id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar rota com id " + req.body
        });
      }
    } else res.send(data);
  });
};

//encerra a rota pelo id
exports.encerrarrota = (req, res) => {
  //validar solicitação
  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"
    });
  }

  var regra = /^[0-9]+$/;

  if(!req.body.id){
    res.status(404).send({
      message: "id não pode ser nulo"
    })
    return;
  }

  valor = req.body.id;
  if (!(valor.match(regra))) {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
       res.status(404).send({
        message: "id deve ser um valor numérico não negativo"
      })
      return;
  }

  if(!req.body.pessoas_abordadas){
    res.status(404).send({
      message: "pessoas_abordadas não pode ser nulo"
    })
    return;
  }

  delete valor;
  valor = req.body.pessoas_abordadas;
  if (!(valor.match(regra))) {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
       res.status(404).send({
        message: "pessoas_abordadas deve ser um valor numérico não negativo"
      })
      return;
  }

  if(!req.body.veiculos_4_rodas){
    res.status(404).send({
      message: "veiculos_4_rodas não pode ser nulo"
    })
    return;
  }

  valor = req.body.veiculos_4_rodas;
  if (!(valor.match(regra))) {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
       res.status(404).send({
        message: "veiculos_4_rodas deve ser um valor numérico não negativo"
      })
      return;
  }

  if(!req.body.veiculos_2_rodas){
    res.status(404).send({
      message: "veiculos_2_rodas não pode ser nulo"
    })
    return;
  }

  valor = req.body.veiculos_2_rodas;
  if (!(valor.match(regra))) {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
       res.status(404).send({
        message: "veiculos_2_rodas deve ser um valor numérico não negativo"
      })
      return;
  }

  if(!req.body.armas_apreendidas){
    res.status(404).send({
      message: "armas_apreendidas não pode ser nulo"
    })
    return;
  }

  valor = req.body.armas_apreendidas;
  if (!(valor.match(regra))) {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
       res.status(404).send({
        message: "armas_apreendidas deve ser um valor numérico não negativo"
      })
      return;
  }

  if(!req.body.apresentacao_na_delegacia){
    res.status(404).send({
      message: "apresentacao_na_delegacia não pode ser nulo"
    })
    return;
  }

  valor = req.body.apresentacao_na_delegacia;
  if (!(valor.match(regra))) {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
       res.status(404).send({
        message: "apresentacao_na_delegacia deve ser um valor numérico não negativo"
      })
      return;
  }

  if(!req.body.estabelecimento_abordado){
    res.status(404).send({
      message: "estabelecimento_abordado não pode ser nulo"
    })
    return;
  }

  valor = req.body.estabelecimento_abordado;
  if (!(valor.match(regra))) {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
       res.status(404).send({
        message: "estabelecimento_abordado deve ser um valor numérico não negativo"
      })
      return;
  }

  if(!req.body.carga_recuperada){
    res.status(404).send({
      message: "carga_recuperada não pode ser nulo"
    })
    return;
  }

  valor = req.body.carga_recuperada;
  if (!(valor.match(regra))) {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
       res.status(404).send({
        message: "carga_recuperada deve ser um valor numérico não negativo"
      })
      return;
  }

  if(!req.body.veiculo_recuperado){
    res.status(404).send({
      message: "veiculo_recuperado não pode ser nulo"
    })
    return;
  }

  valor = req.body.veiculo_recuperado;
  if (!(valor.match(regra))) {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
       res.status(404).send({
        message: "veiculo_recuperado deve ser um valor numérico não negativo"
      })
      return;
  }

  if(!req.body.km_final_viatura){
    res.status(404).send({
      message: "km_final_viatura não pode ser nulo"
    })
    return;
  }

  valor = req.body.km_final_viatura;
  if (!(valor.match(regra))) {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
       res.status(404).send({
        message: "km_final_viatura deve ser um valor numérico não negativo"
      })
      return;
  }

  /*
  if(!req.body.kilometragem_final){
    res.status(404).send({
      message: "campo kilometragem_final não pode ser vazio!"
    })
    return;
  }
  */
  console.log(req.body);
  //verificando se há erros na busca ou na atualização para se não, retornar os dados atualizados
  rota.encerrar_rota(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Não encontrada rota com id ${req.params.atualizarrota}.`
        });
      } else {
        res.status(500).send({
          message: "erro ao encerrar rota com id " + req.params.atualizarrota
        });
      }
    } else res.send(data);
  }
 );
};
// RETIRADO POR EDICAO };

//finaliza uma rota contabilizando (pessoas abordadas, carga recuperada, armas apreendidas, etc.)
exports.finalizar_rota = (req, res) => {
  var regra = /^[0-9]+$/;

  if(!req.body.id){
    res.status(404).send({
      message: "id não pode ser nulo"
    })
    return;
  }

  valor = req.body.id;
  if (!(valor.match(regra))) {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
       res.status(404).send({
        message: "id deve ser um valor numérico não negativo"
      })
      return;
  }

  if(!req.body.pessoas_abordadas){
    res.status(404).send({
      message: "pessoas_abordadas não pode ser nulo"
    })
    return;
  }

  delete valor;
  valor = req.body.pessoas_abordadas;
  if (!(valor.match(regra))) {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
       res.status(404).send({
        message: "pessoas_abordadas deve ser um valor numérico não negativo"
      })
      return;
  }

  if(!req.body.veiculos_4_rodas){
    res.status(404).send({
      message: "veiculos_4_rodas não pode ser nulo"
    })
    return;
  }

  valor = req.body.veiculos_4_rodas;
  if (!(valor.match(regra))) {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
       res.status(404).send({
        message: "veiculos_4_rodas deve ser um valor numérico não negativo"
      })
      return;
  }

  if(!req.body.veiculos_2_rodas){
    res.status(404).send({
      message: "veiculos_2_rodas não pode ser nulo"
    })
    return;
  }

  valor = req.body.veiculos_2_rodas;
  if (!(valor.match(regra))) {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
       res.status(404).send({
        message: "veiculos_2_rodas deve ser um valor numérico não negativo"
      })
      return;
  }

  if(!req.body.armas_apreendidas){
    res.status(404).send({
      message: "armas_apreendidas não pode ser nulo"
    })
    return;
  }

  valor = req.body.armas_apreendidas;
  if (!(valor.match(regra))) {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
       res.status(404).send({
        message: "armas_apreendidas deve ser um valor numérico não negativo"
      })
      return;
  }

  if(!req.body.apresentacao_na_delegacia){
    res.status(404).send({
      message: "apresentacao_na_delegacia não pode ser nulo"
    })
    return;
  }

  valor = req.body.apresentacao_na_delegacia;
  if (!(valor.match(regra))) {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
       res.status(404).send({
        message: "apresentacao_na_delegacia deve ser um valor numérico não negativo"
      })
      return;
  }

  if(!req.body.estabelecimento_abordado){
    res.status(404).send({
      message: "estabelecimento_abordado não pode ser nulo"
    })
    return;
  }

  valor = req.body.estabelecimento_abordado;
  if (!(valor.match(regra))) {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
       res.status(404).send({
        message: "estabelecimento_abordado deve ser um valor numérico não negativo"
      })
      return;
  }

  if(!req.body.carga_recuperada){
    res.status(404).send({
      message: "carga_recuperada não pode ser nulo"
    })
    return;
  }

  valor = req.body.carga_recuperada;
  if (!(valor.match(regra))) {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
       res.status(404).send({
        message: "carga_recuperada deve ser um valor numérico não negativo"
      })
      return;
  }

  if(!req.body.veiculo_recuperado){
    res.status(404).send({
      message: "veiculo_recuperado não pode ser nulo"
    })
    return;
  }

  valor = req.body.veiculo_recuperado;
  if (!(valor.match(regra))) {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
       res.status(404).send({
        message: "veiculo_recuperado deve ser um valor numérico não negativo"
      })
      return;
  }

  if(!req.body.km_final_viatura){
    res.status(404).send({
      message: "km_final_viatura não pode ser nulo"
    })
    return;
  }

  valor = req.body.km_final_viatura;
  if (!(valor.match(regra))) {
    /* Deu ruim,
       isso significa que o valor é negativo
       ou contém caracteres que não são números
    */
       res.status(404).send({
        message: "km_final_viatura deve ser um valor numérico não negativo"
      })
      return;
  }

    console.log(req.body);

    //verificando se há erros na busca ou na atualização para se não, retornar os dados atualizados
    rota.finaliza_rota(req.body, (err, data) => {
      if (err) {
        if (err.kind === "not_found") {
          res.status(404).send({
            message: `Não encontrada rota com id ${req.params.atualizarrota}.`
          });
        } else {
          res.status(500).send({
            message: "erro ao encerrar rota com id " + req.params.atualizarrota
          });
        }
      } else res.send(data);
    });

};

//rota para retornar somatória dos valores lançados no encerramento_rota durante um período**
// exports.relatoriorotas = (req, res) => {

// //   if ((req.params.dataIni) && (req.params.dataFim)) {
// //     res.status(200).send({
// //       message: `Relatório de rotas. ${req.params.dataIni} -- ${req.params.dataFim}`
// //     });
// //   } /*else {
// //     res.status(500).send({
// //       message: `Datas inválidas`
// //     })
// //   }
// //   rota.relatoriorotas_pordata = (params, result) => {
// // */
//   if ((req.params.dataIni) && (req.params.dataFim)) {
//     param = req.params;
//     rota.relatoriorotas_pordata((param, dados) => {
//       if (err)
//         res.status(500).send({
//           message:
//             err.message || "Algum erro ocorreu ao tentar recuperar rotas"
//         });
//       else res.send(data);
//     });
//   }
// };

exports.relatoriorotas = (req, res) => {

  // const isDate = (data) => {
  //   return new Date(data);
  //   // return new Date(isNaN(new Date(date)));
  //   // return (new Date(data) !== "Invalid Date") && !isNaN(new Date(date));
  // }

  const isDate = (x) =>
  {
    moment(x, "YYYY-MM-DD", true).isValid();
    // return (null != x) && !isNaN(x) && ("undefined" !== typeof x.getDate);
  }

  if(!req.query.data_inicio){
    res.status(400).send({
      message: "data_inicio não pode ser nulo"
    })
    return;
  } else {

    // if (!isDate(req.query.data_inicio)){
    //   res.status(400).send({
    //     message: "data_inicio deve ser uma data válida"
    //   })
    //   return;
    // }
  }
  if(!req.query.data_final){
    res.status(400).send({
      message: "data_final não pode ser nulo"
    })
    return;
  } else {

    // if (!isDate(req.query.data_final)){
    //   res.status(400).send({
    //     message: "data_final deve ser uma data válida"
    //   })
    //   return;
    // }
  }

  var tem_area = false;
  if (req.query.id_area){
    var regra = /^[0-9]+$/;
    valor = req.query.id_area;
    if (!(valor.match(regra))) {
      /* Deu ruim,
         isso significa que o valor é negativo
         ou contém caracteres que não são números
      */
         res.status(400).send({
          message: "id_area deve ser um valor numérico não negativo"
        })
        return;
    }
    tem_area = true;
  }

  var tem_unidade = false;
  if (req.query.unidade){
    tem_unidade = true;
  }


  // res.status(200).send({
  //   message: `Relatório de rotas. ${req.query.data_inicio} -- ${req.query.data_final} -- ${req.query.area_atuacao} -- ${req.query.unidade} `
  // });

  // ocorrencia_model.ocorrencias_fazenda(req.query, (err, data) => {
  if ((!tem_area) && (!tem_unidade)){
    rota.relatoriorotas_periodo(req.query, (err, data) => {
      if (err)
        res.status(500).send({
          errmessage:
            err.message || "Ocorreu algum erro ao buscar as ocorrências da fazenda."
        });
      else res.status(200).send(data);
    })
  } else if (!tem_unidade) {
    rota.relatoriorotas_comarea(req.query, (err, data) => {
      if (err)
        res.status(500).send({
          errmessage:
            err.message || "Ocorreu algum erro ao buscar as ocorrências da fazenda."
        });
      else res.status(200).send(data);
    })
  } else if (tem_unidade) {
    rota.relatoriorotas_unidade(req.query, (err, data) => {
      if (err)
        res.status(500).send({
          errmessage:
            err.message || "Ocorreu algum erro ao buscar as ocorrências da fazenda."
        });
      else res.status(200).send(data);
    })
  } else if ((tem_area) && (tem_unidade)){
    rota.relatoriorotas_periodoareaunidade(req.query, (err, data) => {
      if (err)
        res.status(500).send({
          errmessage:
            err.message || "Ocorreu algum erro ao buscar as ocorrências da fazenda."
        });
      else res.status(200).send(data);
    })
  }
}
