const sql = require("./db.js");

    
const qrcode = function (qrcode) {
  
  this.id_fzd = qrcode.id_fzd;
  this.nome_da_fazenda = qrcode.nome_da_fazenda;
  this.data_da_criacao = qrcode.data_da_criacao;
  this.hora_da_criacao = qrcode.hora_da_criacao;
  
};

//salvar qrcode
qrcode.salvar_qrcode = (nova_qrcode, result) => {
  sql.query("INSERT INTO z_qrcode_generator SET ?", nova_qrcode, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    console.log("qrcode criada: ", { id: res.insertId, ...nova_qrcode });
    result(null, { id: res.insertId, ...nova_qrcode });
  });
};

//buscar uma unica qrcode pelo id
qrcode.buscar_qrcode = (json, result) => {
  sql.query(`SELECT * FROM z_qrcode_generator WHERE id = ${json.id}`, (err, res) => {
    //se ocorrer um erro
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca da qrcode suceder
    if (res.length) {
      console.log("qrcode encontrada: ", res[0]);
      result(null, res[0]);
      return;
    }

    //quando não encontrada a qrcode pelo id
    result({ kind: "not_found" }, null);
  });
};

//buscar todas as qrcodes cadastradas
qrcode.todas_qrcodes = result => {
  sql.query("SELECT * FROM z_qrcode_generator;", (err, res) => {
    if (err) {
      //se ocorrer um erro
      console.log("error: ", err);
      result(null, err);
      return;
    }
    //se a busca de todas as qrcodes suceder 
    console.log("customers: ", res);
    result(null, res);
  });
};

qrcode.atualizar_qrcode = (qrcode, result) => {
  console.log(qrcode)
  sql.query(
    "UPDATE z_qrcode_generator SET id_fzd = ?, nome_da_fazenda = ?, data_da_criacao = ?, hora_da_criacao = ? WHERE id = ?",
    [qrcode.id_fzd, qrcode.nome_da_fazenda, qrcode.data_da_criacao, qrcode.hora_da_criacao, qrcode.id],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }

      if (res.affectedRows == 0) {
        //não encontrada qrcode com o id
        result({ kind: "qrcode não encontrada" }, null);
        return;
      }

      result(null, "OK" );
    }
  );
};

// //delete uma qrcode com o id especificado na chamada
// Customer.remove = (id, result) => {
//   sql.query("DELETE FROM customers WHERE id = ?", id, (err, res) => {
//     if (err) {
//       console.log("error: ", err);
//       result(null, err);
//       return;
//     }

//     if (res.affectedRows == 0) {
//       // not found Customer with the id
//       result({ kind: "not_found" }, null);
//       return;
//     }

//     console.log("deleted customer with id: ", id);
//     result(null, res);
//   });
// };

// //delete todas as qrcodes do banco de dados.
// Customer.removeAll = result => {
//   sql.query("DELETE FROM customers", (err, res) => {
//     if (err) {
//       console.log("error: ", err);
//       result(null, err);
//       return;
//     }

//     console.log(`deleted ${res.affectedRows} customers`);
//     result(null, res);
//   });
// };

module.exports = qrcode;

