const Autentication = require("../models/autentication.model.js");
const Customer = require("../models/autentication.model.js");
var requestIp = require('request-ip');
require('dotenv').config()
let date_ob = new Date();

// current date
// adjust 0 before single digit date
let date = ("0" + date_ob.getDate()).slice(-2);

// current month
let month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
// current year
let year = date_ob.getFullYear();
// current hours
let hours = date_ob.getHours();
// current minutes
let minutes = date_ob.getMinutes();
// current seconds
let seconds = date_ob.getSeconds();

var date_now = year + "-" + month + "-" + date + " " + hours + ":" + minutes + ":" + seconds

const client = require('twilio')(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

exports.chave = (req, res) => {
  var clientIp = requestIp.getClientIp(req);
  Autentication.Gera_Chave(clientIp, (err, data) => {
    if (err)
      res.status(500).send({
        message: err.message || "Some error occurred while retrieving customers."
      });
    else
      res.status(200).send({


        key: data
        // res.send(data)
      });
    console.log("KEY>>>>>>>>>" + data);

  });
};


exports.auth = (req, res) => {

  console.log(req.body)
  if (!req.body) {
    res.status(400).send({
      message: "Content can not be empty!"
    });
    return;
  }

  var clientIp = requestIp.getClientIp(req);

  console.log(clientIp)

  // Create a Customer
  const usuario = new Autentication({
    usuario: req.body.usuario,
    senha: req.body.senha,
    chave: req.body.chave
  });


  Autentication.black_list(clientIp, (err, data) => {
    //se o ip do resquest estiver na black list..
    if (data) {
      res.status(200).send({
        message: "BLOCKED"
      });
      return;
    }

    //se não vamos seguir..
    //vamos verificar se o usuario existe.
    Autentication.selecionar_usuario(usuario, (err, data_user) => {

      if (data_user) {
        //vamos verificar se o usuario não esta bloqueado por numero de tentativas
        if (data_user.STATUS == "1") {
          console.log("dados usuario >>>>>>>>>>>>>>>>>>>>>>>>>", usuario)

          Autentication.chave_api(usuario, (err, data) => {

            var expira = Date(data.DATA_HORA_EXPIRA);
            var atual = Date(date_now);

            //vamos ver se esta no prazo e se o usuario ainda não foi bloqueado por tentar 3 vezes
            if (expira >= atual && data.STATUS == "REQ") {

              Autentication.update_chave_user(data_user, data, (err, data) => {
              });

              res.status(200).send({
                data_user
              });

              return;
            } else {
              res.status(200).send({
                message: "EXPIRED"
              });
              return;
            }

          });

        } else {
          //numero de tentativas 
          res.status(200).send({
            message: "EXCEEDED"
          });
          return;
        }
      } else {
        res.status(200).send({
          message: "INVALID"
        });
        return;
      }

    });
  });
};

exports.aceitar_termos = (req, res) => {
  if(!req.body.id_usuario){
    res.status(500).send({
      message: "Houve um erro ao aceitar os termos"
    })
  }
  Autentication.aceitar_termos(req.body.id_usuario, (err, data_user) => {
    res.status(200).send({
      message: "Termo de uso aceito com sucesso"
    })
  })
}


exports.dispara_sms = (req, res) => {

  CODE = Math.floor(Math.random() * 10000 + 1)
  console.log(CODE)

  client.messages
    .create({
      from: process.env.TWILIO_PHONE_NUMBER,
      to: req.body.number,
      body: "Seu codigo de acesso: " + CODE
    })
    .then(() => {

      Autentication.update_code_sms(req.body, CODE, (err, data) => {
      });

      res.status(200).send({
        message: "OK"
      });

    })
    .catch(err => {
      res.status(500).send({
        message: err.message
      });
    });

};



exports.cheq_sms = (req, res) => {

  Autentication.chek_sms(req.body, (err, data) => {
    if (err)
      res.status(500).send({
        message: err.message || "Some error occurred while retrieving customers."
      });
    else

      //vamos checar se esta dentro do prazo
      if (data) {
        res.status(200).send({
          message: "OK"
        });
      } else {
        res.status(200).send({
          message: "INVALID"
        });
      }

  });

};



