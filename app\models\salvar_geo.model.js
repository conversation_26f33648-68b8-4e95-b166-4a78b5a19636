const sql = require("./db.js");

const geolocalizacao = function (geolocalizacao) {
  this.ip = geolocalizacao.ip;
  this.latitude = geolocalizacao.latitude;
  this.longitude = geolocalizacao.longitude;
  this.id_usuario = geolocalizacao.id_usuario;
  this.data_hora = geolocalizacao.data_hora;
};

geolocalizacao.salvar_geolocalizacao = (nova_geo, result) => {
  sql.query("INSERT INTO tbl_geolocalizacao SET ?", nova_geo, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    result(null, {"response": {"latitude": nova_geo.latitude, "longitude": nova_geo.longitude} });
  });
};

module.exports = geolocalizacao;