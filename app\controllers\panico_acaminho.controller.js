const Panico_acaminho = require("../models/panico_acaminho.model.js");
var requestIp = require('request-ip');

exports.disparo_panico_acaminho = (req, res) => {

  console.log(req.body)
  var clientIp = requestIp.getClientIp(req);
  if (!req.body) {
    res.status(400).send({
      message: "Content can not be empty!"
    });

    return;
  }

  const panico_acaminho_model = new Panico_acaminho({
    id: req.body.id,
    id_viatura: req.body.id_viatura
  });

  Panico_acaminho.disparo(panico_acaminho_model, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while creating the Customer."
      });
    else
      // socketio.emit('Atenção viatura a caminho', { "Aviso": "Teste", "id_alerta": data });
      res.send({ "resposta": "OK" });
  });

};
