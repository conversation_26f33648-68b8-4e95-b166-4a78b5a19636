const request = require("supertest");

test("requisição HTTP deve retornar http status code 200", () => {
    expect(true).toBe(true);
});

// describe("POST /enviar_viatura", () => {
//     // test("requisição HTTP deve retornar http status code 200", async () => {
//     //     const response = await request("http://localhost:12103").post("/enviar_viatura")
//     //     .send({
//     //         "id_viat": 2,
//     //         "id_ocorrencia": 1
//     //     })
//     //     expect(response.statusCode).toBe(200);
//     })
// })