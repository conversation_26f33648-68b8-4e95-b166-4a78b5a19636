const sql = require("./db.js");

// constructor
const Panico_ocorrencias_armada = function(panico_ocorrencias_armada) {
  this.id = panico_ocorrencias_armada.id;
};

Panico_ocorrencias_armada.getAll = result => {
  sql.query("SELECT * FROM `tbl_panico` INNER JOIN `api_user` ON (`tbl_panico`.`USER_ID` = `api_user`.`ID_USUARIO`)where `tbl_panico`.`STATUS` = '0';", (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(null, err);
      return;
    }
    console.log("customers: ", res);
    result(null, res);
  });
};


module.exports = Panico_ocorrencias_armada;
