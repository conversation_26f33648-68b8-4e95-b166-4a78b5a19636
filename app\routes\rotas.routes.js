module.exports = app => {

    const Rotas = require("../controllers/rotas.controller.js");

    //rota para salvar uma nova rota
    app.post("/salvar_rota", Rotas.salvarrota)

    //rota para buscar uma rota pelo id***
    app.post("/buscar_rota", Rotas.buscarrota)

    //rota para detalhar os componentes da viatura na operação na rota
    app.post("/detalhe_responsavel", Rotas.detalheresponsavel)

    //busca todas as rotas criadas pelo id da fazenda
    app.post("/detalhe_visita", Rotas.detalhevisita)

    //rota para retornar todas as rotas
    app.get("/todas_rotas", Rotas.todasrotas)

    //atualizar os dados de uma rota a partir do seu id
    app.put("/atualizar_rota", Rotas.atualizarrota)

    //atualizar os dados de uma rota a partir do seu id
    app.put("/atualizar_rota_2", Rotas.atualizarrota_2)

    //salva a etapa 2 da rota a partir do seu id
    app.put("/salvar_rota2", Rotas.salvarrota2)

    //inicia o serviço da rota e salva o restante das etapas na rota
    app.put("/salvar_rota3", Rotas.salvarrota3)

    // Delete a Customer with customerId
    app.delete("/deletar_rota", Rotas.deletarrota);

    //rota para buscar uma fazenda pelo id da rota**
    app.post("/buscar_fazenda_rota", Rotas.buscarfazendarota)

    //inicia o serviço da rota e salva o restante das etapas na rota
    app.put("/encerrar_rota", Rotas.encerrarrota)

    //obtém o relatório de rotas filtrando por DataInicial, DataFinal, Unidade e Rota
    app.get("/relatorio_rotas", Rotas.relatoriorotas);

    // app.get("/relatorio_rotas/dataInicial/:dataIni/dataFinal/:dataFim/areaAtuacao/:areaAtuacao/unidade/:unidade", Rotas.relatoriorotas)
    // //obtém o relatório de rotas filtrando por DataInicial, DataFinal e Área de Atuação
    // app.get("/relatorio_rotas/dataInicial/:dataIni/dataFinal/:dataFim/areaAtuacao/:areaAtuacao", Rotas.relatoriorotas)
    // //obtém o relatório de rotas filtrando por DataInicial, DataFinal e Rota
    // app.get("/relatorio_rotas/dataInicial/:dataIni/dataFinal/:dataFim/unidade/:unidade", Rotas.relatoriorotas)
    // //obtém o relatório de rotas filtrando por DataInicial, DataFinal
    // app.get("/relatorio_rotas/dataInicial/:dataIni/dataFinal/:dataFim", Rotas.relatoriorotas)

  };
