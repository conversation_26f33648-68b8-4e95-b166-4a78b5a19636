const sql = require("./db.js");
const socket = require("../../server.js");

const Panico_acaminho = function (panico_acaminho) {
    this.id = panico_acaminho.id;
    this.id_viatura = panico_acaminho.id_viatura;
};

Panico_acaminho.disparo = (panico_acaminho, result) => {

    sql.query(`UPDATE db_app_aiba.tbl_panico SET STATUS = '1', VIATURA = ${panico_acaminho.id_viatura} WHERE ID = ${panico_acaminho.id}`, (err, res) => {
        if (err) {
            console.log("error: ", err);
            result(null, err);
            return;
        }

        if (res.affectedRows == 0) {
            result({ kind: "not_found" }, null);
            return;
        }
        result(null, {"resposta":"OK"});

        socket.panico_socket_acaminho(panico_acaminho.id);
    }
    );
};

module.exports = Panico_acaminho;
