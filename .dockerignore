# Node.js
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files (keep .env for production secrets)
.env.local
.env.development
.env.test
*.log

# Git
.git
.gitignore

# Documentation
README.md
LICENSE.md
docs/

# Test files
__tests__
*.test.js
jest.config.js
coverage/

# Development tools
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp

# Build artifacts (if any)
dist/
build/

# Deployment configs (keep nixpacks.toml for reference but exclude from Docker)
nixpacks.toml

# Backup files
bkpdopackage.json

# Docker files
Dockerfile
.dockerignore