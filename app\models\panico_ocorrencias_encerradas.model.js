const sql = require("./db.js");

// constructor
const Panico_ocorrencias_encerradas = function(panico_ocorrencias_encerradas) {
  this.id = panico_ocorrencias_encerradas.id;
};

Panico_ocorrencias_encerradas.getAll = result => {
  sql.query("SELECT * FROM `tbl_panico` INNER JOIN `api_user` ON (`tbl_panico`.`USER_ID` = `api_user`.`ID_USUARIO`)where `tbl_panico`.`STATUS` = '2';", (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(null, err);
      return;
    }
    console.log("customers: ", res);
    result(null, res);
  });
};

module.exports = Panico_ocorrencias_encerradas;
