// Temporary CORS debug configuration
// Use this to debug CORS issues, then switch back to restrictive CORS

const corsDebugOptions = {
  origin: true, // Allow all origins temporarily
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  preflightContinue: false,
  optionsSuccessStatus: 200
};

const socketCorsDebugOptions = {
  cors: {
    origin: true, // Allow all origins temporarily
    credentials: true,
    methods: ['GET', 'POST']
  }
};

module.exports = {
  corsDebugOptions,
  socketCorsDebugOptions
};
