module.exports = app => {
  
    const Log_visitas = require("../controllers/log_visita_fazenda.controller.js");
  
    //rota para salvar uma nova log_visita 
    app.post("/salvar_log_visita", Log_visitas.salvarlogvisita)

    //rota para buscar uma log_visita pelo id
    app.post("/buscar_log_visita", Log_visitas.buscarlogvisita)

    //rota para retornar todas as log_visitas
    app.get("/todas_log_visitas", Log_visitas.todaslogvisitas)

    //atualizar os dados de uma log_visita a partir do seu id
    app.put("/atualizar_log_visita", Log_visitas.atualizarlogvisita)  
    
    // Delete a Customer with customerId
    app.delete("/deletar_log_visita", Log_visitas.deletarlogvisita)

 
  };
  