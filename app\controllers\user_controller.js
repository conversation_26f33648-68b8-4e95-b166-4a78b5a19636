const user_model = require("../models/user_model.js");

exports.user_insert = (req, res) => {

  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"
    });
    return;
  }

  const new_user = new user_model({
    ID_USUARIO: req.body.ID_USUARIO,
    ID_FAZENDA: req.body.ID_FAZENDA,
    NOME: req.body.NOME,
    USUARIO: req.body.USUARIO,
    SENHA: req.body.SENHA,
    EMAIL: req.body.EMAIL,
    TELEFONE: req.body.TELEFONE,
    PERMISSAO: req.body.PERMISSAO,
    STATUS: req.body.STATUS,
    IDENTIFICACAO: req.body.IDENTIFICACAO
  });

  user_model.insert_user(new_user, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Ocorreu algum erro ao criar user."
      });
    else res.send(data);
  });

};







