module.exports = app => {
  
    const Fazendas = require("../controllers/fazendas.controller.js");
  
    //rota para salvar uma nova fazenda 
    app.post("/salvar_fazenda", Fazendas.salvarfazenda)

    //rota para buscar uma fazenda pelo id
    app.post("/buscar_fazenda", Fazendas.buscarfazenda)

    //rota para buscar Extras de uma fazenda pelo id
    app.post("/buscar_extras", Fazendas.buscarextras)

    //CRIAR UMA rota para atualizar apenas os extras pelo id da fazenda
    app.put("/atualizar_extras", Fazendas.atualizarextras)

    //rota para buscar fazendas de um proprietario pelo id
    app.post("/buscar_fazenda_proprietario", Fazendas.buscarfazendaproprietario)

    //rota para buscar fazendas pelo id da area de atuação
    app.post("/buscar_fazendas_atuacao", Fazendas.buscarfazendas)

    //rota para retornar todas as fazendas
    app.get("/todas_fazendas", Fazendas.todasfazendas)

    app.get("/visitas_fazendas", Fazendas.visitas_fazendas)

    //atualizar os dados de uma fazenda a partir do seu id
    app.put("/atualizar_fazenda", Fazendas.atualizarfazenda)  
    
    // Delete a Customer with customerId
    app.delete("/deletar_fazenda", Fazendas.deletarfazenda);

 
  }; 
  