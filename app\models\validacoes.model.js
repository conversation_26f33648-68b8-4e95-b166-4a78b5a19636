const sql = require("./db.js");

let date_ob = new Date();
let date = ("0" + date_ob.getDate()).slice(-2);
let month = ("0" + (date_ob.getMonth() + 1)).slice(-2);
let year = date_ob.getFullYear();
let hours = date_ob.getHours();
let minutes = date_ob.getMinutes();
let seconds = date_ob.getSeconds();
var date_now = year + "-" + month + "-" + date;
var hour_now = hours + ":" + minutes + ":" + seconds;

const validacao = function (validacao) {
  this.latitude = validacao.latitude;
  this.longitude = validacao.longitude;
  this.status_visita = validacao.status_visita;
  this.data_validacao = validacao.data_validacao;
  this.hora_validacao = validacao.hora_validacao;
  this.id_viatura = validacao.id_viatura;  
  this.fzd_id = validacao.fzd_id;
  this.id_rota = validacao.id_rota;
  this.viatura = validacao.viatura;
  this.pernoite = validacao.pernoite;
  this.combustivel = validacao.combustivel;

};

//salvar validacao
validacao.salvar_validacao = (nova_validacao, result) => {
  sql.query("INSERT INTO z_visitas_validacao SET ?", nova_validacao, (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }

    console.log("visita realizada com sucesso!", { id: res.insertId, ...nova_validacao });
    result(null, { id: res.insertId, ...nova_validacao });
  });
};

//buscar uma unica validacao pelo id
validacao.buscar_validacao = (json, result) => {
  sql.query(`SELECT * FROM z_visitas_validacao WHERE id = ${json.fzd_id}`, (err, res) => {
    //se ocorrer um erro
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca da validacao suceder
    if (res.length) {
      console.log("validacao encontrada: ", res[0]);
      result(null, res[0]);
      return;
    }

    //quando não encontrada a validacao pelo id
    result({ kind: "not_found" }, null);
  });
};

//buscar as validacoes relacionadas ao proprietario pelo id proprietario????
validacao.buscar_validacao_proprietario = (json, result) => {
  sql.query(`SELECT * FROM db_app_aiba.z_visitas_validacao WHERE id = '${json.id}'`, (err, res) => {
    //se ocorrer um erro
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca da validacao suceder
    if (res.length) {
      console.log("validacao(s) encontrada(s): ", res);
      result(null, res);
      return;
    }

    //quando não encontrada a validacao pelo id
    result({ kind: "not_found" }, null);
  });
};


//buscar uma unica validacao pelo id
validacao.buscar_validacoes = (json, result) => {
  sql.query(`SELECT * FROM db_app_aiba.z_visitas_validacao where z_visitas_validacao.id  = ${json.id}`, (err, res) => {
    //se ocorrer um erro
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca da validacao suceder
    if (res.length) {
      console.log("validacao encontrada: ", res);
      result(null, res);
      return;
    }

    //quando não encontrada a validacao pelo id
    result({ kind: "not_found" }, null);
  });
};

//buscar todas as validacoes cadastradas
validacao.todas_validacoes = result => {
  sql.query("SELECT * FROM z_visitas_validacao;", (err, res) => {
    if (err) {
      //se ocorrer um erro
      console.log("error: ", err);
      result(null, err);
      return;
    }
    //se a busca de todas as validacoes suceder 
    console.log("validacoes: ", res);
    result(null, res);
  });
};

//atualiza os dados de uma validacao pelo seu id
validacao.atualizar_validacao = (validacao, result) => {
  console.log(validacao)
  sql.query(
    "UPDATE z_visitas_validacao SET latitude = ?, longitude = ?, status_visita = ?, data_validacao = ?, hora_validacao = ?, id_viatura = ?, viatura = ?, id_fzd = ? WHERE id = ?",
    [validacao.latitude, validacao.longitude, validacao.status_visita, validacao.data_validacao, validacao.hora_validacao, validacao.id_viatura, validacao.viatura, validacao.id_fzd,validacao.id],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }

      if (res.affectedRows == 0) {
        //não encontrada validacao com o id
        result({ kind: "validacao não encontrada" }, null);
        return;
      }

      result(null, "OK" );
    }
  );
};

//delete uma validacao com o id especificado na chamada
validacao.deletar_validacao = (id, result) => {

  sql.query("DELETE FROM `db_app_aiba`.`z_visitas_validacao` WHERE z_visitas_validacao.id_fzd = ?;", id, (err, res) => {
    if (err) {
      result(err, null);
      return;
    }

    if (res.affectedRows == 0) {
      // not found validacao with the id
      result({ kind: "not_found" }, null);
      return;
    }

    console.log("deleted validacao with id: ", id);
    result(null, res);
  });
  
};


module.exports = validacao;


