const sql = require("./db.js");
const socket = require("../../server.js");



// constructor
const Panico_finalizado = function (panico_finalizado) {
    this.id = panico_finalizado.id;
    this.resumo = panico_finalizado.resumo;
};

Panico_finalizado.disparo = (panico_finalizado, result) => {
//
    console.log(panico_finalizado)
    sql.query(`UPDATE db_app_aiba.tbl_panico SET STATUS='2', resumo_panico = '${panico_finalizado.resumo}' WHERE ID = ${panico_finalizado.id}`, (err, res) => {
        if (err) {
            console.log("error: ", err);
            result(null, err);
            return;
        }

        if (res.affectedRows == 0) {
            result({ kind: "not_found" }, null);
            return;
        }
        result(null, {"resposta":"OK"});

        socket.panico_socket_finalizado(panico_finalizado.id)
    }
    );
};

module.exports = Panico_finalizado;
