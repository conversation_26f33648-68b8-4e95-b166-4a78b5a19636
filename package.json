{"name": "course-player-socketio", "version": "1.0.0", "description": "Course Player built with Socket.IO", "main": "server.js", "scripts": {"start": "node swagger.js && node server.js", "start:dev": "node-dev server.js", "test": "jest", "swagger-autogen": "node swagger.js"}, "keywords": [], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"axios": "^0.27.2", "cors": "^2.8.5", "dotenv": "^10.0.0", "express": "^4.17.1", "express-pino-logger": "^6.0.0", "ipware": "^2.0.0", "moment": "^2.29.1", "moment-timezone": "^0.5.33", "mysql": "^2.18.1", "node-dev": "^7.4.3", "nodemailer": "^6.7.0", "react-moment": "^1.1.1", "request-ip": "^2.1.3", "socket.io": "^4.2.0", "ssl-root-cas": "^1.3.1", "swagger-autogen": "^2.13.2", "swagger-ui-express": "^4.2.0", "time-delta": "^1.0.0", "token": "^0.1.0", "ts-node-dev": "^1.1.8", "twilio": "^3.67.1"}, "devDependencies": {"jest": "^27.4.2"}, "engines": {"node": ">=18.0.0"}}