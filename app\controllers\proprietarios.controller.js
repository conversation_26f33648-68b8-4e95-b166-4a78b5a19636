const proprietario = require("../models/proprietarios.model.js");
const proprietario_model = require("../models/proprietarios.model.js");



// Cria e salva uma nova proprietario
exports.salvarproprietario = (req, res) => {
  
 
  if (!req.body) {
    res.status(400).send({
      message: "Content can not be empty!"
     
    });

    return;
  }

  // Cria uma proprietario

  const proprietario = new proprietario_model({
    nome_do_proprietario: req.body.nome_do_proprietario,
    cpf: req.body.cpf,
    email: req.body.email,
    telefone: req.body.telefone,
    data_de_nascimento: req.body.data_de_nascimento,
    foto_do_proprietario: req.body.foto_do_proprietario,
    qtde_fazendas: req.body.qtde_fazendas
  });

  // Salva proprietario no banco de dados
  proprietario_model.salvar_proprietario(proprietario, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Algum erro ocorreu ao criar o proprietario."
      });
    else res.send(data);
  });

};

// Busca uma unica proprietario pelo seu id
exports.buscarproprietario = (req, res) => {
  // console.log(req.body)
  proprietario.buscar_proprietario(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrada proprietario com id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar proprietario com id " + req.body
        });
      }
    } else res.send(data);
  });
};

//mostra todos os proprietarios cadastrados
exports.todosproprietarios = (req, res) => {
  proprietario.todos_proprietarios((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Algum erro ocorreu ao tentar recuperar proprietarios"
      });
    else res.send(data);
  });
};

//atualiza os dados de um proprietario identificado pelo id na chamada
exports.atualizarproprietario= (req, res) => {
  //validar solicitação
  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"
    });
  }

  console.log(req.body);
//verificando se há erros na busca ou na atualização para se não, retornar os dados atualizados
  proprietario.atualizar_proprietario(req.body, (err, data) => {
      if (err) {
        if (err.kind === "not_found") {
          res.status(404).send({
            message: `Não encontrado proprietario com id ${req.params.atualizarproprietario}.`
          });
        } else {
          res.status(500).send({
            message: "erro ao atualizar proprietario com id " + req.params.atualizarproprietario
          });
        }
      } else res.send(data);
    }
  );
};


// //delete um proprietario com o id especificado na chamada
// exports.delete = (req, res) => {
//   Customer.remove(req.params.customerId, (err, data) => {
//     if (err) {
//       if (err.kind === "not_found") {
//         res.status(404).send({
//           message: `Not found Customer with id ${req.params.customerId}.`
//         });
//       } else {
//         res.status(500).send({
//           message: "Could not delete Customer with id " + req.params.customerId
//         });
//       }
//     } else res.send({ message: `Customer was deleted successfully!` });
//   });
// };


// //delete todos os proprietarios do banco de dados.
// exports.deleteAll = (req, res) => {
//   Customer.removeAll((err, data) => {
//     if (err)
//       res.status(500).send({
//         message:
//           err.message || "Some error occurred while removing all customers."
//       });
//     else res.send({ message: `All Customers were deleted successfully!` });
//   });
// };

//envia email para o proprietario
exports.prepare_email = (req, res) => {

  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"

    });
    return;
  }

  const mail = new mail_model({
    email: req.body.email,
    texto: req.body.texto
  });
  
  mail_model.fire_email(mail, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Ocorreu algum erro ao criar rota."
      });
    else res.send(data);
  });

};
