# Use Node.js 18 LTS Alpine for smaller image size and better security
FROM node:18-alpine

# Install system dependencies that might be needed
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# Set working directory
WORKDIR /app

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Copy package files first for better Docker layer caching
COPY package*.json ./

# Install dependencies with more verbose output for debugging
RUN npm config set loglevel info && \
    npm install --only=production --no-optional && \
    npm cache clean --force

# Copy application code
COPY . .

# Generate Swagger documentation (before switching to non-root user)
RUN node swagger-build.js

# Change ownership to nodejs user
RUN chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose the port (using environment variable with fallback)
EXPOSE 80

# Health check to ensure the application is running
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:' + (process.env.PORT || 80) + '/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"

# Start the application
CMD ["npm", "start"]
