const Panico_finalizado = require("../models/panico_finalizado.model.js");
var requestIp = require('request-ip');

exports.disparo_panico_finalizado = (req, res) => {

  console.log(req.body)

  var clientIp = requestIp.getClientIp(req);
  if (!req.body) {
    res.status(400).send({
      message: "Content can not be empty!"
    });

    return;
  }

  if(!req.body.resumo) {
    res.status(400).send({
      message: "Resumo não pode estar vazio"
    })

    return;
  }

  const panico_finalizado = new Panico_finalizado({
    id: req.body.id,
    resumo: req.body.resumo
  });

  Panico_finalizado.disparo(panico_finalizado, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Some error occurred while creating the Customer."
      });
    else
      // socketio.emit('Atenção viatura a caminho', { "Aviso": "Teste", "id_alerta": data });
      res.send({ "resposta": "OK" });
  });

};
