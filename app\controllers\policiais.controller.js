const policial = require("../models/policiais.model.js");
const policial_model = require("../models/policiais.model.js");



// Cria e salva um novo policial
exports.salvarpolicial = (req, res) => { 
 
  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"
     
    });

    return;
  }

  const policial = new policial_model({
    matricula: req.body.matricula,
    senha: req.body.senha,
    posto_graduacao: req.body.posto_graduacao,
    nome: req.body.nome,
    foto: req.body.foto,
    unidade: req.body.unidade,
    email: req.body.email,
    telefone: req.body.telefone,
    coordenador: req.body.coordenador,
    ativo_inativo: req.body.ativo_inativo
  });

  // Salva policial no banco de dados
  policial_model.salvar_policial(policial, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Algum erro ocorreu ao criar o policial."
      });
    else res.send(data);
  });

};

// Busca um unico policial pelo seu id
exports.buscarpolicial = (req, res) => {
  // console.log(req.body)
  policial.buscar_policial(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrado policial com id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar policial com id " + req.body
        });
      }
    } else res.send(data);
  });
};

//mostra todas as policiais cadastradas
exports.todospoliciais = (req, res) => {

  policial.todos_policiais((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Algum erro ocorreu ao tentar recuperar policiais"
      });
    else res.send(data);
  });
};

//atualiza os dados de uma policial identificada pelo id na chamada
exports.atualizarpolicial = (req, res) => {
  //validar solicitação
  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"
    });
  }

  console.log(req.body);
//verificando se há erros na busca ou na atualização para se não, retornar os dados atualizados
  policial.atualizar_policial(req.body, (err, data) => {
      if (err) {
        if (err.kind === "not_found") {
          res.status(404).send({
            message: `Não encontrada policial com id ${req.params.atualizarpolicial}.`
          });
        } else {
          res.status(500).send({
            message: "erro ao atualizar policial com id " + req.params.atualizarpolicial
          });
        }
      } else res.send(data);
    }
  );
};

// //delete uma policial com o id especificado na chamada
// exports.delete = (req, res) => {
//   Customer.remove(req.params.customerId, (err, data) => {
//     if (err) {
//       if (err.kind === "not_found") {
//         res.status(404).send({
//           message: `Not found Customer with id ${req.params.customerId}.`
//         });
//       } else {
//         res.status(500).send({
//           message: "Could not delete Customer with id " + req.params.customerId
//         });
//       }
//     } else res.send({ message: `Customer was deleted successfully!` });
//   });
// };


// //delete todas as policials do banco de dados.
// exports.deleteAll = (req, res) => {
//   Customer.removeAll((err, data) => {
//     if (err)
//       res.status(500).send({
//         message:
//           err.message || "Some error occurred while removing all customers."
//       });
//     else res.send({ message: `All Customers were deleted successfully!` });
//   });
// };

// Busca um unico policial pelo seu id
exports.todospmunidade = (req, res) => {
  // console.log(req.body)
  policial.todospm_unidade(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrado policiais da unidade pelo id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar policiais da unidade pelo id " + req.body
        });
      }
    } else res.send(data);
  });
};

//envia email para o pm
exports.prepare_email = (req, res) => {

  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"

    });
    return;
  }

  const mail = new mail_model({
    email: req.body.email,
    texto: req.body.texto
  });
  
  mail_model.fire_email(mail, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Ocorreu algum erro ao criar rota."
      });
    else res.send(data);
  });

};
 //envia email para o pm coordenador
exports.prepare_email = (req, res) => {

  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"

    });
    return;
  }

  const mail = new mail_model({
    email: req.body.email,
    texto: req.body.texto
  });
  
  mail_model.fire_email(mail, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Ocorreu algum erro ao criar rota."
      });
    else res.send(data);
  });

};
