const swaggerAutogen = require('swagger-autogen')()

const doc = {
    info: {
      title: 'My API',
      description: 'Description',
    },
    host: process.env.HOST,
    schemes: ['http', 'https'],
  };

const outputFile = './swagger_output.json'
const endpointsFiles = ["./app/routes/customer.routes.js",
                        "./app/routes/autentication.routes.js",
                        "./app/routes/fazendas.routes.js",
                        "./app/routes/panico.routes.js",
                        "./app/routes/proprietarios.routes.js",
                        "./app/routes/viaturas.routes.js",
                        "./app/routes/policiais.routes.js",
                        "./app/routes/area_atuacao.routes.js",
                        "./app/routes/qrcode_gen.routes.js",
                        "./app/routes/log_visita_fazenda.routes.js",
                        "./app/routes/rotas.routes.js",
                        "./app/routes/geolocalizacao.routes",
                        "./app/routes/ocorrencias.routes",
                        "./app/routes/informacoes.routes",
                        "./app/routes/iniciacao.routes",
                        "./app/routes/validacoes.routes",
                        "./app/routes/user_routes",
                        "./app/routes/send_mail.routes"]

// Generate swagger documentation without starting the server
swaggerAutogen(outputFile, endpointsFiles, doc).then(() => {
    console.log('Swagger documentation generated successfully!')
    process.exit(0)
}).catch((error) => {
    console.error('Error generating swagger documentation:', error)
    process.exit(1)
})
