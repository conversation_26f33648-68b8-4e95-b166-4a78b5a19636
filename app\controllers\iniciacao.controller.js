const iniciacao = require("../models/iniciacao.model.js");
const iniciacao_model = require("../models/iniciacao.model.js");



// Cria e salva uma nova iniciacao
exports.salvariniciacao = (req, res) => {


  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"

    });

    return;
  }

  // Cria uma iniciacao
  const iniciacao = new iniciacao_model({
    id_viatura: req.body.id_viatura,
    // viatura: req.body.viatura,
    id_agente_responsavel: req.body.id_agente_responsavel,
    // agente_responsavel: req.body.agente_responsavel,
    // data_inicio: req.body.data_inicio,
    // hora_inicio: req.body.hora_inicio,
    // data_final: req.body.data_final,
    // hora_final: req.body.hora_final,
    quilometragem: req.body.quilometragem,
    // agentes_na_viatura: req.body.agentes_na_viatura,
    id_rota: req.body.id_rota
    
  });

  // Salva iniciacao no banco de dados
  iniciacao_model.salvar_iniciacao(iniciacao, (err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Ocorreu algum erro ao criar iniciacao."
      });
    else res.send(data);
  });

};

// Busca uma unica iniciacao pelo seu id
exports.buscariniciacao = (req, res) => {

  iniciacao.buscar_iniciacao(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrada iniciacao com id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar iniciacao com id " + req.body
        });
      }
    } else res.send(data);
  });
};

// Busca iniciacoes do proprietario pelo seu id
exports.buscariniciacaoproprietario = (req, res) => {

  iniciacao.buscar_iniciacao_proprietario(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrada iniciacao com id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar iniciacao com id " + req.body
        });
      }
    } else res.send(data);
  });
};


// Busca as iniciacoes pelo id da area de atuação
exports.buscariniciacoes = (req, res) => {

  iniciacao.buscar_iniciacoes(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Nao encontrada iniciacao com id ${req.body}.`
        });
      } else {
        res.status(500).send({
          message: "Erro ao retornar iniciacao com id " + req.body
        });
      }
    } else res.send(data);
  });
};


//mostra todas as iniciacoes cadastradas
exports.todasiniciacoes = (req, res) => {
  iniciacao.todas_iniciacoes((err, data) => {
    if (err)
      res.status(500).send({
        message:
          err.message || "Algum erro ocorreu ao tentar recuperar iniciacoes"
      });
    else res.send(data);
  });
};

//atualiza os dados de uma iniciacao identificada pelo id na chamada
exports.atualizariniciacao = (req, res) => {
  //validar solicitação
  if (!req.body) {
    res.status(400).send({
      message: "Conteudo não pode estar vazio!"
    });
  }

  console.log(req.body);
  //verificando se há erros na busca ou na atualização para se não, retornar os dados atualizados
  iniciacao.atualizar_iniciacao(req.body, (err, data) => {
    if (err) {
      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Não encontrada iniciacao com id ${req.params.atualizariniciacao}.`
        });
      } else {
        res.status(500).send({
          message: "erro ao atualizar iniciacao com id " + req.params.atualizariniciacao
        });
      }
    } else res.send(data);
  }
  );
};

//delete uma iniciacao com o id especificado na chamada
exports.deletariniciacao = (req, res) => {

  iniciacao.deletar_iniciacao(req.body.id, (err, data) => {

    if (err) {

      if (err.kind === "not_found") {
        res.status(404).send({
          message: `Not found Customer with id ${req.params.customerId}.`
        });
      } else {
        res.status(500).send({
          message: "Could not delete Customer with id " + req.params.customerId
        });
      }
      
    } else res.send({ message: `Customer was deleted successfully!` });
    
  });

};





