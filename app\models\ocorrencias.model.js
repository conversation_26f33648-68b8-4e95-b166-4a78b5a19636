const { ocorrencias_viatura } = require("../controllers/ocorrencias.controller.js");
const sql = require("./db.js");


const ocorrencia = function (ocorrencia) {
  this.id_prop = ocorrencia.id_prop;
  this.motivo = ocorrencia.motivo;
  this.descricao = ocorrencia.descricao;
  this.onde_registrou = ocorrencia.onde_registrou;
  this.dia = ocorrencia.dia;
  this.hora = ocorrencia.hora;

};//

ocorrencia.motivos_ocorrencia = result => {
  sql.query("select * from motivos_ocorrencia", (err, res) => {
    if(err){
      result(err, null);
      return;
    }
    if(res.length){
      result(null, {"Motivos": res})
      return;
    }
    
  })
}

ocorrencia.registrar_ocorrencia = (json, result) => {
  sql.query(`INSERT INTO ocorrencias (id_prop, id_fzd, id_motivo, descricao, registro_delegacia) VALUES (
      ${json.id_prop},
      ${json.id_fzd},
      ${json.id_motivo},
      "${json.descricao}",
      ${json.registro_delegacia}
    );`, (err, res) => {
      if(err){
        result(err, null);
        return;
      }
      result(null, {id: res.insertId, ...json});
      return;
    })
}

ocorrencia.buscar_ocorrencia = (query, result) => {
  let resposta = [];
  if(query.status){
    sql.query(`SELECT * FROM ocorrencias WHERE status = '${query.status}' ORDER BY created_at DESC`, (err, res) => {
      if(err){
        result(err, null);
        return;
      }
      if(res.length){
        res.map(res => {
          const objeto = {
            id: res.id,
            id_prop: res.id_prop,
            id_fzd: res.id_fzd,
            id_motivo: res.id_motivo,
            descricao: res.descricao,
            id_viat: res.id_viat,
            registro_delegacia: res.registro_delegacia[0] == 0 ? false : true,
            observacao: res.observacao,
            status: res.status,
            created_at: res.created_at,
            updated_at: res.updated_at
          }
          resposta.push(objeto)
        })
        result(null, resposta);
        return
      }
  
      result(null, {
        "message" : "Não há ocorrencias com o status " + query.status
      })
      return;
    })
  }
  else{
    sql.query(`SELECT * FROM ocorrencias ORDER BY created_at DESC`, (err, res) => {
      if(err){
        result(err, null);
        return;
      }
      if(res.length){
        res.map(res => {
          const objeto = {
            id: res.id,
            id_prop: res.id_prop,
            id_fzd: res.id_fzd,
            id_motivo: res.id_motivo,
            descricao: res.descricao,
            id_viat: res.id_viat,
            registro_delegacia: res.registro_delegacia[0] == 0 ? false : true,
            observacao: res.observacao,
            status: res.status,
            created_at: res.created_at,
            updated_at: res.updated_at
          }
          resposta.push(objeto)
        })
        result(null, resposta);
        return
      }
  
      result(null, {
        "message" : "Não há ocorrências"
      })
      return;
    })
  }
  
}

ocorrencia.enviar_viatura = (json, result) => {
  sql.query(`UPDATE ocorrencias set status = 'Em atendimento', id_viat = ${json.id_viat} WHERE id = ${json.id_ocorrencia}`, (err, res) => {
    if (err) {
      result(err, null)
      return;
    }
    result(null, {message: `Ocorrencia com id ${json.id_ocorrencia} atribuida com sucesso a viatura ${json.id_viat}`});
    return;
  });
  
}

ocorrencia.concluir_ocorrencia = (json, result) => {
  sql.query(`select * from ocorrencias where id = ${json.id_ocorrencia}`, (err, res) => {
    if(res[0].status !== "Em atendimento"){
      result({message: "Ocorrência não pode ser concluída se o status for diferente de Em atendimento"}, null)
      return;
    }else{
      sql.query(`UPDATE ocorrencias set status = "Concluída", observacao = "${json.justificativa}" WHERE id = ${json.id_ocorrencia}`, (err, res) => {
        if (err) {
          result(err, null)
          return;
        }
        result(null, {message: `Ocorrencia com id ${json.id_ocorrencia} concluída com sucesso`});
        return;
      })
    }
  })
}

ocorrencia.detalhe_ocorrencia = (json, result) => {
  sql.query(`select * from ocorrencias where id = ${json.id_ocorrencia}`, (err, res) => {
    if (err) {
      result(err, null)
      return;
    }
    result(null, {
      id: res[0].id,
      id_prop: res[0].id_prop,
      id_fzd: res[0].id_fzd,
      id_motivo: res[0].id_motivo,
      descricao: res[0].descricao,
      id_viat: res[0].id_viat,
      registro_delegacia: res[0].registro_delegacia[0] == 0 ? false : true,
      observacao: res[0].observacao,
      status: res[0].status,
      created_at: res[0].created_at,
      updated_at: res[0].updated_at
    });
    return;
  })
}

ocorrencia.ocorrencias_viatura = (query, result) => {
  let resposta = []
  sql.query(`select * from ocorrencias where status = "Em atendimento" and id_viat = ${query.id_viat} ORDER BY created_at DESC`, (err, res) => {
    if (err) {
      result(err, null)
      return;
    }

    if(res.length){
      res.map(res => {
        const objeto = {
          id: res.id,
          id_prop: res.id_prop,
          id_fzd: res.id_fzd,
          id_motivo: res.id_motivo,
          descricao: res.descricao,
          id_viat: res.id_viat,
          registro_delegacia: res.registro_delegacia[0] == 0 ? false : true,
          observacao: res.observacao,
          status: res.status,
          created_at: res.created_at,
          updated_at: res.updated_at
        }
        resposta.push(objeto)
      })
      result(null, resposta);
      return
    }
    else{
      result(null, {message: "Não há ocorrências Em atendimento para a viatura " + query.id_viat})
    }

  })
}

ocorrencia.cancelar_ocorrencia = (json, result) => {
  // console.log(`UPDATE ocorrencias set status = "Cancelada", observacao = "${json.justificativa}" WHERE id = ${json.id_ocorrencia} `);
  sql.query(`select * from ocorrencias where id = ${json.id_ocorrencia}`, (err, res) => {
    if(res[0].status === "Concluída" || res[0].status === "Cancelada"){
      result({message: "Ocorrência não pode ser cancelada se o status for concluída ou cancelada"}, null)
      return;
    }else{
      sql.query(`UPDATE ocorrencias set status = "Cancelada", observacao = "${json.justificativa}" WHERE id = ${json.id_ocorrencia}`, (err, res) => {
        if (err) {
          result(err, null)
          return;
        }
        result(null, {message: `Ocorrencia com id ${json.id_ocorrencia} cancelada com sucesso `});
        return;
      })
    }
  })
}

ocorrencia.ocorrencias_fazenda = (query, result) => {
  let resposta = []
  sql.query(`select * from ocorrencias where id_fzd = ${query.id_fzd} ORDER BY created_at DESC`, (err, res) => {
    if (err) {
      result(err, null)
      return;
    }

    if(res.length){
      res.map(res => {
        const objeto = {
          id: res.id,
          id_prop: res.id_prop,
          id_fzd: res.id_fzd,
          id_motivo: res.id_motivo,
          descricao: res.descricao,
          id_viat: res.id_viat,
          registro_delegacia: res.registro_delegacia[0] == 0 ? false : true,
          observacao: res.observacao,
          status: res.status,
          created_at: res.created_at,
          updated_at: res.updated_at
        }
        resposta.push(objeto)
      })
      result(null, resposta);
      return;
      
    }
    else{
      result({message: "Não há ocorrências para a fazenda " + query.id_fzd}, null)
    }
  })
}

module.exports = ocorrencia;
