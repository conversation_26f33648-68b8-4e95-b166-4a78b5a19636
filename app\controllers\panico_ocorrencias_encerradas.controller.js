const Panico_ocorrencias_encerradas = require("../models/panico_ocorrencias_encerradas.model.js");

// Retrieve all Customers from the database.
exports.findAll = (req, res) => {
    Panico_ocorrencias_encerradas.getAll((err, data) => {
        if (err)
            res.status(500).send({
                message:
                    err.message || "Some error occurred while retrieving customers."
            });
        else res.send(data);
    });
};

