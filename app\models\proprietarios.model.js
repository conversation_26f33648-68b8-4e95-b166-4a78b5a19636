const sql = require("./db.js");
const server = require("../../server.js");
const nodemailer = require("nodemailer");


const proprietario = function (proprietario) {
  this.nome_do_proprietario = proprietario.nome_do_proprietario;
  this.cpf = proprietario.cpf;
  this.email = proprietario.email;
  this.telefone = proprietario.telefone;
  this.data_de_nascimento = proprietario.data_de_nascimento;
  this.foto_do_proprietario = proprietario.foto_do_proprietario;
  this.qtde_fazendas = proprietario.qtde_fazendas;
  
};

//salvar proprietario
proprietario.salvar_proprietario = (novo_proprietario, result) => {
  sql.query(`SELECT * FROM z_proprietarios_cadastrados where cpf = ${novo_proprietario.cpf}`, (err, res) => {
    if(!res.length) {
      sql.query("INSERT INTO z_proprietarios_cadastrados SET ?", novo_proprietario, (err, res) => {
        if (err) {
          console.log("error: ", err);
          result(err, null);
          return;
        }
        console.log("proprietario criado: ", { id: res.insertId, ...novo_proprietario });
        result(null, { id: res.insertId, ...novo_proprietario });
        
        // server.send_mail(novo_proprietario.email, "Ola, seu usuario e senha no AIBA é:");
    
      });
    }
    else{
      result(null, {
        errmessage: "Proprietário já cadastrado com este CPF!"
      });
    }
  })
};

//buscar um unic proprietario pelo id
proprietario.buscar_proprietario = (json, result) => {
  sql.query(`SELECT * FROM z_proprietarios_cadastrados WHERE id_prop = ${json.id_prop}`, (err, res) => {
    //se ocorrer um erro
    if (err) {
      console.log("error: ", err);
      result(err, null);
      return;
    }
    //se a busca do proprietario suceder
    if (res.length) {
      // console.log("proprietario encontrado: ", res[0]);
      result(null, res[0]);
      return;
    }

    //quando não encontrado o proprietario pelo id
    result({ kind: "not_found" }, null);
  });
};

//buscar todas as proprietarios cadastradas
proprietario.todos_proprietarios = result => {
  sql.query("SELECT * FROM z_proprietarios_cadastrados;", (err, res) => {
    if (err) {
      //se ocorrer um erro
      console.log("error: ", err);
      result(null, err);
      return;
    }
    //se a busca de todas as proprietarios suceder 
    // console.log("proprietarios: ", res);
    result(null, res);
  });
};

//atualiza os dados de uma proprietario pelo seu id
proprietario.atualizar_proprietario = (proprietario, result) => {
  console.log(proprietario)
  sql.query(
    "UPDATE z_proprietarios_cadastrados SET nome_do_proprietario = ?, cpf = ?, email = ?, telefone = ?, data_de_nascimento = ?, foto_do_proprietario = ?, qtde_fazendas = ? WHERE id_prop = ?",
    [proprietario.nome_do_proprietario, proprietario.cpf, proprietario.email, proprietario.telefone, proprietario.data_de_nascimento, proprietario.foto_do_proprietario, proprietario.qtde_fazendas, proprietario.id_prop],
    (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      }

      if (res.affectedRows == 0) {
        //não encontrada proprietario com o id
        result({ kind: "proprietario não encontrado" }, null);
        return;
      }

      result(null, "OK" );
    }
  );
};

// //delete uma proprietario com o id especificado na chamada
// Customer.remove = (id, result) => {
//   sql.query("DELETE FROM customers WHERE id = ?", id, (err, res) => {
//     if (err) {
//       console.log("error: ", err);
//       result(null, err);
//       return;
//     }

//     if (res.affectedRows == 0) {
//       // not found Customer with the id
//       result({ kind: "not_found" }, null);
//       return;
//     }

//     console.log("deleted customer with id: ", id);
//     result(null, res);
//   });
// };

// //delete todas as proprietarios do banco de dados.
// Customer.removeAll = result => {
//   sql.query("DELETE FROM customers", (err, res) => {
//     if (err) {
//       console.log("error: ", err);
//       result(null, err);
//       return;
//     }

//     console.log(`deleted ${res.affectedRows} customers`);
//     result(null, res);
//   });
// };

//enviar um email para o proprietario

const mail_send = require("../../server.js");

const model_email = function (email) {
  this.email = email.proprietario.email;
  this.texto = email.texto;
};

model_email.fire_email = (email, result) => {
    mail_send.send_mail(email.email, email.texto);
    result(null, {"response": "OK"});
};

function send_mail(mail, text) {
  let testAccount = nodemailer.createTestAccount();
  testAccount.user = "<EMAIL>"
  testAccount.pass = "**************************************************"

  // create reusable transporter object using the default SMTP transport
  let transporter = nodemailer.createTransport({
    host: "smtp.mailgun.org",
    port: 587,
    secure: false, // true for 465, false for other ports
    auth: {
      user: testAccount.user, // generated ethereal user
      pass: testAccount.pass, // generated ethereal password
    },
  });

  
  // send mail with defined transport object
  let info = transporter.sendMail({
    from: '<EMAIL>', 
    to: mail.email, 
    subject: "Hello ✔", 
    text: text, 
    html: "<b>Hello world?</b>", 
  });

  console.log("Message sent: %s", info.messageId);
  // Message sent: <<EMAIL>>

  // Preview only available when sending through an Ethereal account
  console.log("Preview URL: %s", nodemailer.getTestMessageUrl(info));
  // Preview URL: https://ethereal.email/message/WaQKMgKddxQDoou...
};


module.exports = proprietario;

